// 使用"ui"模式
"ui";

/**
 * 脚本助手 - 模块化版本
 * 基于AutoX.js开发的安卓脚本软件
 */



/**
 * 检测是否为系统文本（包括聊天气泡时间）
 */
function isSystemText(text) {
    if (!text) return true;

    // 系统文本特征
    var systemPatterns = [
        /^\d{1,2}:\d{2}$/,                    // 基础时间格式 12:34
        /^上午\d{1,2}:\d{2}$/,                // 上午时间 上午9:30
        /^下午\d{1,2}:\d{2}$/,                // 下午时间 下午2:15
        /^晚上\d{1,2}:\d{2}$/,                // 晚上时间 晚上6:49
        /^凌晨\d{1,2}:\d{2}$/,                // 凌晨时间 凌晨1:23
        /^\d{1,2}月\d{1,2}日$/,               // 日期格式 12月25日
        /^昨天|今天|星期|刚才|分钟前|小时前/,      // 时间相关
        /^[\d\s\-:：]+$/,                     // 纯数字符号
        /^[\.。，,！!？?]+$/,                  // 纯标点
        /撤回|重新编辑|查看详情|小程序/,          // 系统功能
        /微信|返回|语音通话|视频通话|拍摄|相册|表情/, // 界面元素
        /^[a-zA-Z]$/,                        // 单个字母
        /^[\u4e00-\u9fa5]$/,                 // 单个汉字
        /^\d{4}年\d{1,2}月\d{1,2}日$/,        // 完整日期 2024年1月1日
        /^周[一二三四五六日]$/,                // 星期 周一
        /^星期[一二三四五六日]$/               // 星期 星期一
    ];

    for (var i = 0; i < systemPatterns.length; i++) {
        if (systemPatterns[i].test(text)) {
            return true;
        }
    }

    return false;
}

/**
 * 检测是否在头像区域
 */
function isAvatarArea(bounds, chatRegion) {
    if (!bounds) return false;

    var relativeX = bounds.left;
    var width = bounds.right - bounds.left;
    var height = bounds.bottom - bounds.top;

    // 头像通常是正方形，位于消息两侧
    var isSquareish = Math.abs(width - height) < Math.min(width, height) * 0.3;
    var isSmall = width < 80 && height < 80;
    var isAtSide = relativeX < chatRegion.width * 0.15 || relativeX > chatRegion.width * 0.85;

    // 如果是类似正方形且在两侧，可能是头像
    if (isSquareish && isSmall && isAtSide) {
        return true;
    }

    return false;
}

/**
 * 检测是否为聊天气泡上方的时间显示
 */
function isChatBubbleTime(text, bounds, chatRegion) {
    if (!text || !bounds) return false;

    // 1. 首先检查文本是否符合时间格式
    var timePatterns = [
        /^\d{1,2}:\d{2}$/,                    // 12:34
        /^上午\d{1,2}:\d{2}$/,                // 上午9:30
        /^下午\d{1,2}:\d{2}$/,                // 下午2:15
        /^晚上\d{1,2}:\d{2}$/,                // 晚上6:49
        /^凌晨\d{1,2}:\d{2}$/,                // 凌晨1:23
        /^\d{1,2}月\d{1,2}日$/,               // 12月25日
        /^昨天|今天$/,                        // 昨天/今天
        /^周[一二三四五六日]$/,                // 周一
        /^星期[一二三四五六日]$/               // 星期一
    ];

    var isTimeFormat = false;
    for (var i = 0; i < timePatterns.length; i++) {
        if (timePatterns[i].test(text)) {
            isTimeFormat = true;
            break;
        }
    }

    if (!isTimeFormat) return false;

    // 2. 检查位置特征
    var relativeX = bounds.left;
    // var relativeY = bounds.top;
    var width = bounds.right - bounds.left;
    var height = bounds.bottom - bounds.top;

    // 聊天气泡时间的位置特征：
    // - 通常位于屏幕中央区域（水平居中）
    // - 文字较小
    // - 可能位于聊天内容的上方
    var isCentered = relativeX > chatRegion.width * 0.2 && relativeX < chatRegion.width * 0.8;
    var isSmallText = width < 200 && height < 50;

    // 3. 综合判断（时间格式 + 居中 + 小文字）
    if (isTimeFormat && isCentered && isSmallText) {
        // console.log("检测到聊天气泡时间: \"" + text + "\" 位置: (" + relativeX + "," + relativeY + ")");
        return true;
    }

    return false;
}

/**
 * 清理文本内容，提高识别精度（优化链接处理）
 */
function cleanTextContent(text) {
    if (!text) return "";

    // 1. 基础清理
    text = text.trim();

    // 2. 检测是否为链接文本
    var isLinkText = /https?|www|[。\.]com|[。\.]cn|[。\.]net|[。\.]org|baidu|google/.test(text);

    if (isLinkText) {
        // 链接文本的特殊处理：保持英文标点
        // console.log("检测到链接文本，应用链接清理: " + text);

        // 修复链接中的中文标点
        text = text.replace(/。/g, ".");
        text = text.replace(/，/g, ",");
        text = text.replace(/：/g, ":");
        text = text.replace(/；/g, ";");
        text = text.replace(/？/g, "?");
        text = text.replace(/！/g, "!");

        // 修复常见域名
        text = text.replace(/baidu[。\.]com/g, "baidu.com");
        text = text.replace(/www[。\.]baidu/g, "www.baidu");

    } else {
        // 普通文本的标点符号统一
        text = text.replace(/[，,]\s*/g, "，");     // 统一逗号
        text = text.replace(/[。.]\s*/g, "。");     // 统一句号
        text = text.replace(/[！!]\s*/g, "！");     // 统一感叹号
        text = text.replace(/[？?]\s*/g, "？");     // 统一问号
        text = text.replace(/[：:]\s*/g, "：");     // 统一冒号
        text = text.replace(/[；;]\s*/g, "；");     // 统一分号
    }

    // 3. 修复常见OCR错误（链接文本需要特别小心）
    if (!isLinkText) {
        // 只对非链接文本进行字符修复，避免破坏链接路径

        // 修复数字和字母混淆
        text = text.replace(/[oO]/g, function (match, offset, string) {
            var before = string.charAt(offset - 1);
            var after = string.charAt(offset + 1);
            if (/\d/.test(before) && /\d/.test(after)) {
                return "0";
            }
            return match;
        });

        // 修复I/l/1混淆
        text = text.replace(/[Il|]/g, function (match, offset, string) {
            var before = string.charAt(offset - 1);
            var after = string.charAt(offset + 1);
            if (/\d/.test(before) && /\d/.test(after)) {
                return "1";
            }
            return match;
        });
    } else {
        // 链接文本只做最基础的修复
        // console.log("跳过链接文本的字符修复，保护路径完整性");
    }

    // 4. 清理多余空格
    text = text.replace(/\s+/g, " ").trim();

    return text;
}

/**
 * 专门的链接文本修复函数（优化标点符号识别）
 */
function fixUrlText(text) {
    if (!text) return "";

    // console.log("链接修复前: " + text);

    // 1. 修复中文标点符号为英文标点（链接专用）
    text = text.replace(/。/g, ".");           // 中文句号 → 英文点号
    text = text.replace(/，/g, ",");           // 中文逗号 → 英文逗号
    text = text.replace(/：/g, ":");           // 中文冒号 → 英文冒号
    text = text.replace(/；/g, ";");           // 中文分号 → 英文分号
    text = text.replace(/？/g, "?");           // 中文问号 → 英文问号
    text = text.replace(/！/g, "!");           // 中文感叹号 → 英文感叹号

    // 2. 修复协议部分
    text = text.replace(/https?\s*:\s*\/\s*\/\s*/g, "https://");
    text = text.replace(/http\s*:\s*\/\s*\/\s*/g, "http://");

    // 3. 修复www部分（处理中文句号）
    text = text.replace(/www\s*[。\.]\s*/g, "www.");

    // 4. 修复域名和后缀（处理中文句号）
    text = text.replace(/[。\.]\s*(com|cn|net|org|edu|gov|mil|io|co|html|php|asp|jsp)\b/g, ".$1");

    // 5. 修复链接中的多个点号
    text = text.replace(/([a-zA-Z0-9])[。\.]+([a-zA-Z0-9])/g, "$1.$2");

    // 6. 修复路径分隔符
    text = text.replace(/\/\s+/g, "/");
    text = text.replace(/\s+\//g, "/");

    // 7. 修复查询参数
    text = text.replace(/\?\s+/g, "?");
    text = text.replace(/&\s+/g, "&");
    text = text.replace(/=\s+/g, "=");

    // 8. 修复端口号
    text = text.replace(/:\s*(\d+)/g, ":$1");

    // 9. 移除链接中的多余空格
    text = text.replace(/([a-zA-Z0-9])\s+([a-zA-Z0-9\.\-\/])/g, "$1$2");

    // 10. 修复常见的OCR字符错误（避免链接路径中的误修复）
    text = text.replace(/[Il1|]/g, function (match, offset, string) {
        var before = string.charAt(offset - 1);
        var after = string.charAt(offset + 1);
        // 只在明确的数字上下文中替换，避免链接路径中的误替换
        if (/\d/.test(before) && /\d/.test(after)) {
            return "1";
        }
        return match;
    });

    // 11. 修复0和O的混淆（避免链接路径中的误修复）
    text = text.replace(/[oO]/g, function (match, offset, string) {
        var before = string.charAt(offset - 1);
        var after = string.charAt(offset + 1);
        // 只在明确的数字上下文中替换，避免将单词中的o替换成0
        if (/\d/.test(before) && /\d/.test(after)) {
            return "0";
        }
        // 特殊情况：端口号
        if (before === ':' && /\d/.test(after)) {
            return "0";
        }
        return match;
    });

    // 12. 特殊修复：常见域名的标点错误
    text = text.replace(/baidu[。\.]com/g, "baidu.com");
    text = text.replace(/google[。\.]com/g, "google.com");
    text = text.replace(/qq[。\.]com/g, "qq.com");
    text = text.replace(/sina[。\.]com/g, "sina.com");
    text = text.replace(/163[。\.]com/g, "163.com");
    text = text.replace(/taobao[。\.]com/g, "taobao.com");
    text = text.replace(/jd[。\.]com/g, "jd.com");

    // 13. 修复协议后的标点
    text = text.replace(/(https?:\/\/[^。]*)[。]([a-zA-Z])/g, "$1.$2");

    // 14. 保护常见的链接路径单词，避免被OCR修复破坏
    text = protectLinkWords(text);

    // console.log("链接修复后: " + text);
    return text;
}

/**
 * 保护链接中的常见单词，避免被OCR修复破坏
 */
function protectLinkWords(text) {
    if (!text) return "";

    // 常见的链接路径单词保护
    var protectedWords = [
        { wrong: "inf0", correct: "info" },
        { wrong: "h0me", correct: "home" },
        { wrong: "ab0ut", correct: "about" },
        { wrong: "c0ntact", correct: "contact" },
        { wrong: "pr0duct", correct: "product" },
        { wrong: "serv1ce", correct: "service" },
        { wrong: "n0tice", correct: "notice" },
        { wrong: "pr0file", correct: "profile" },
        { wrong: "l0gin", correct: "login" },
        { wrong: "reg1ster", correct: "register" },
        { wrong: "d0wnload", correct: "download" },
        { wrong: "upl0ad", correct: "upload" },
        { wrong: "real1ty", correct: "reality" },
        { wrong: "gr0up", correct: "group" },
        { wrong: "1ndex", correct: "index" },
        { wrong: "deta1l", correct: "detail" },
        { wrong: "l1st", correct: "list" },
        { wrong: "v1ew", correct: "view" },
        { wrong: "ed1t", correct: "edit" },
        { wrong: "add", correct: "add" },
        { wrong: "del", correct: "del" },
        { wrong: "upd", correct: "upd" }
    ];

    for (var i = 0; i < protectedWords.length; i++) {
        var word = protectedWords[i];
        var regex = new RegExp("\\b" + word.wrong + "\\b", "gi");
        if (regex.test(text)) {
            // console.log("修复链接单词: " + word.wrong + " → " + word.correct);
            text = text.replace(regex, word.correct);
        }
    }

    return text;
}

/**
 * 测试链接识别
 */
function testLinkRecognition() {
    console.log("开始链接识别测试...");

    try {
        // 检查截图权限
        console.log("正在检查截图权限...");

        // 先尝试直接截图，如果失败再请求权限
        let testScreenshot = null;
        try {
            testScreenshot = captureScreen();
            if (testScreenshot) {
                console.log("✓ 截图权限已存在");
                testScreenshot.recycle(); // 释放测试截图
            } else {
                throw new Error("需要请求权限");
            }
        } catch (e) {
            console.log("需要请求截图权限...");
            if (!requestScreenCapture()) {
                console.log("❌ 需要截图权限");
                return [];
            }
            console.log("✓ 截图权限获取成功");
        }

        // 优化：快速截图
        var screenshot = captureScreen();
        if (!screenshot) {
            return [];
        }

        // 优化：精确定位聊天区域，减少处理范围
        var chatRegion = {
            x: Math.floor(device.width * 0.1),   // 优化：稍微增加左边距
            y: Math.floor(device.height * 0.2),  // 优化：从20%开始，避免顶部干扰
            width: Math.floor(device.width * 0.8), // 优化：减少宽度到80%
            height: Math.floor(device.height * 0.6) // 优化：减少高度到60%
        };

        var chatImage = images.clip(screenshot,
            chatRegion.x, chatRegion.y,
            chatRegion.width, chatRegion.height);

        if (!chatImage) {
            screenshot.recycle();
            return [];
        }

        // 优化：增强图像预处理提高识别准确度
        try {
            // 应用灰度处理
            if (typeof images.grayscale === 'function') {
                var grayImage = images.grayscale(chatImage);
                if (grayImage) {
                    chatImage.recycle();
                    chatImage = grayImage;
                }
            }

            // 优化：应用对比度增强
            if (typeof images.adaptiveThreshold === 'function') {
                var enhancedImage = images.adaptiveThreshold(chatImage, 255, "ADAPTIVE_THRESH_GAUSSIAN_C", "THRESH_BINARY", 11, 2);
                if (enhancedImage) {
                    chatImage.recycle();
                    chatImage = enhancedImage;
                }
            } else if (typeof images.threshold === 'function') {
                // 备选方案：普通二值化
                var binaryImage = images.threshold(chatImage, 128, 255, "BINARY");
                if (binaryImage) {
                    chatImage.recycle();
                    chatImage = binaryImage;
                }
            }
        } catch (e) {
            // 图像处理失败时继续使用原图
        }

        // 优化：OCR识别
        var ocrResult = paddle.ocr(chatImage);

        if (!ocrResult || ocrResult.length === 0) {
            chatImage.recycle();
            screenshot.recycle();
            return [];
        }

        // 优化：高精度文本过滤，提高识别准确度
        var textCandidates = [];
        var filteredCount = {
            system: 0,
            avatar: 0,
            time: 0,
            lowConfidence: 0
        };

        for (var i = 0; i < ocrResult.length; i++) {
            var item = ocrResult[i];
            var text = item.text.trim();

            if (!text || text.length === 0) continue;

            // 优化：提高置信度要求到85%，平衡准确度和识别率
            if (item.confidence < 0.85) {
                filteredCount.lowConfidence++;
                continue;
            }

            if (isSystemText(text)) {
                filteredCount.system++;
                // console.log("过滤系统文本: \"" + text + "\"");
                continue;
            }

            if (isAvatarArea(item.bounds, chatRegion)) {
                filteredCount.avatar++;
                // console.log("过滤头像区域: \"" + text + "\"");
                continue;
            }

            if (isChatBubbleTime(text, item.bounds, chatRegion)) {
                filteredCount.time++;
                // console.log("过滤聊天时间: \"" + text + "\"");
                continue;
            }

            // 通过所有过滤条件的文本
            var actualX = chatRegion.x + (item.bounds.left + item.bounds.right) / 2;
            var actualY = chatRegion.y + (item.bounds.top + item.bounds.bottom) / 2;

            // 检查是否包含链接特征（包括中文标点）
            var hasLinkFeatures = /https?|www|[。\.]com|[。\.]cn|[。\.]net|[。\.]org|[。\.]io|[。\.]co|\/\/|[。\.]html|[。\.]php|baidu|google|qq|sina|163|taobao|jd/.test(text);

            // 清理和优化文本
            var cleanedText = cleanTextContent(text);

            if (cleanedText && cleanedText.length > 0) {
                textCandidates.push({
                    text: cleanedText,
                    originalText: text,
                    confidence: item.confidence,
                    x: actualX,
                    y: actualY,
                    bounds: item.bounds,
                    width: item.bounds.right - item.bounds.left,
                    height: item.bounds.bottom - item.bounds.top,
                    hasLinkFeatures: hasLinkFeatures
                });

                // var marker = hasLinkFeatures ? "🔗" : "📝";
                // console.log(marker + " \"" + cleanedText + "\" 位置: (" + Math.round(actualX) + "," + Math.round(actualY) +
                //            ") 置信度: " + Math.round(item.confidence * 100) + "%");
            }
        }
        // console.log("\n✅ 文本过滤完成，保留有效候选: " + textCandidates.length + " 个");

        if (textCandidates.length === 0) {
            chatImage.recycle();
            screenshot.recycle();
            return [];
        }

        // 按Y坐标排序
        textCandidates.sort(function (a, b) { return a.y - b.y; });

        // 智能分组聊天气泡中的多行文本
        var messageGroups = smartGroupChatBubbles(textCandidates);

        // 只选择第一个聊天气泡
        var finalMessages = [];
        if (messageGroups.length > 0) {
            var firstGroup = messageGroups[0];
            var message = createChatBubbleMessage(firstGroup);
            if (message) {
                finalMessages.push(message);
            }
        }

        // 释放资源
        chatImage.recycle();
        screenshot.recycle();

        return finalMessages;

    } catch (e) {
        // console.error("❌ 测试失败: " + e.message);
        return [];
    }
}

/**
 * 智能分组聊天气泡中的多行文本
 */
function smartGroupChatBubbles(items) {
    if (!items || items.length === 0) return [];

    var groups = [];
    var currentGroup = [];

    // 动态计算分组参数
    var avgHeight = 0;
    var avgWidth = 0;
    for (var i = 0; i < items.length; i++) {
        avgHeight += items[i].height;
        avgWidth += items[i].width;
    }
    avgHeight /= items.length;
    avgWidth /= items.length;

    // 聊天气泡分组参数（高置信度模式）
    var lineSpacing = Math.max(30, avgHeight * 1.5);     // 行间距
    var horizontalThreshold = Math.max(60, avgWidth * 0.4); // 水平阈值
    // var bubbleWidth = avgWidth * 3;                      // 气泡宽度估算

    // console.log("聊天气泡分组参数（高置信度≥90%）:");
    // console.log("- 行间距阈值: " + Math.round(lineSpacing));
    // console.log("- 水平阈值: " + Math.round(horizontalThreshold));
    // console.log("- 气泡宽度: " + Math.round(bubbleWidth));
    // console.log("- 置信度要求: ≥90%");

    for (var i = 0; i < items.length; i++) {
        var item = items[i];

        if (currentGroup.length === 0) {
            currentGroup.push(item);
        } else {
            var lastItem = currentGroup[currentGroup.length - 1];
            var yDiff = item.y - lastItem.y;
            var xDiff = Math.abs(item.x - lastItem.x);

            var isSameBubble = false;

            // 条件1: Y坐标递增且间距合理（下一行）
            if (yDiff > 0 && yDiff < lineSpacing) {
                // 条件2: X坐标位置相近（同一气泡）
                if (xDiff < horizontalThreshold) {
                    isSameBubble = true;
                }
            }

            // 条件3: 检查是否在同一个气泡区域内
            if (!isSameBubble && yDiff > 0 && yDiff < lineSpacing * 1.8) {
                // 计算文本区域重叠
                var lastLeft = lastItem.x - lastItem.width / 2;
                var lastRight = lastItem.x + lastItem.width / 2;
                var itemLeft = item.x - item.width / 2;
                var itemRight = item.x + item.width / 2;

                var overlapLeft = Math.max(lastLeft, itemLeft);
                var overlapRight = Math.min(lastRight, itemRight);
                var overlapWidth = Math.max(0, overlapRight - overlapLeft);

                var minWidth = Math.min(lastItem.width, item.width);
                var overlapRatio = overlapWidth / minWidth;

                if (overlapRatio > 0.3) { // 有足够重叠
                    isSameBubble = true;
                }
            }

            // 条件4: 特殊情况 - 链接或长文本
            if (!isSameBubble) {
                var hasLinkInGroup = currentGroup.some(function (g) { return g.hasLinkFeatures; }) || item.hasLinkFeatures;
                var hasLongText = currentGroup.some(function (g) { return g.text.length > 20; }) || item.text.length > 20;

                if ((hasLinkInGroup || hasLongText) && yDiff > 0 && yDiff < lineSpacing * 2.5 && xDiff < horizontalThreshold * 2) {
                    isSameBubble = true;
                }
            }

            // 条件5: 防止跨气泡合并（距离过远）
            if (isSameBubble && (yDiff > lineSpacing * 2 || xDiff > horizontalThreshold * 2)) {
                isSameBubble = false;
            }

            if (isSameBubble) {
                currentGroup.push(item);
                // console.log("合并到气泡: \"" + item.text + "\" (Y差:" + Math.round(yDiff) + ", X差:" + Math.round(xDiff) + ")");
            } else {
                if (currentGroup.length > 0) {
                    groups.push(currentGroup);
                }
                currentGroup = [item];
                // console.log("新气泡开始: \"" + item.text + "\"");
            }
        }
    }

    if (currentGroup.length > 0) {
        groups.push(currentGroup);
    }

    // console.log("分组结果: " + groups.length + " 个聊天气泡");
    // for (var i = 0; i < groups.length; i++) {
    //     console.log("气泡 " + (i + 1) + ": " + groups[i].length + " 行文本");
    // }

    return groups;
}

/**
 * 创建聊天气泡消息（智能拼接多行文本）
 */
function createChatBubbleMessage(group) {
    if (!group || group.length === 0) return null;

    // 按Y坐标排序确保正确的行顺序
    group.sort(function (a, b) { return a.y - b.y; });

    var combinedText = "";
    var totalConfidence = 0;
    var hasLink = false;

    // console.log("拼接气泡文本，共 " + group.length + " 行:");

    for (var i = 0; i < group.length; i++) {
        var text = group[i].text.trim();
        // console.log("  行 " + (i + 1) + ": \"" + text + "\"");

        if (i > 0) {
            // 保持换行格式，在每行之间添加换行符
            combinedText += "\n";
        }

        combinedText += text;
        totalConfidence += group[i].confidence;

        if (group[i].hasLinkFeatures) {
            hasLink = true;
        }
    }

    var avgConfidence = totalConfidence / group.length;

    // 高置信度检查 - 只输出置信度≥90%的消息
    if (avgConfidence < 0.9) {
        // console.log("跳过低置信度气泡: \"" + combinedText.substring(0, 30) + "...\" (置信度: " + Math.round(avgConfidence * 100) + "%)");
        return null;
    }

    // 最终文本清理
    combinedText = finalTextCleanup(combinedText);

    // 应用链接修复
    if (hasLink) {
        combinedText = fixUrlText(combinedText);
    }

    // console.log("拼接结果: \"" + combinedText + "\" (置信度: " + Math.round(avgConfidence * 100) + "%)");

    return {
        text: combinedText,
        confidence: avgConfidence,
        lineCount: group.length,
        isLink: hasLink,
        isMultiline: group.length > 1
    };
}

/**
 * 最终文本清理（优化链接处理）
 */
function finalTextCleanup(text) {
    if (!text) return "";

    // 1. 检测是否包含链接
    var hasLink = /https?:\/\/|www\.|\.com|\.cn|\.net|\.org/.test(text);

    // 2. 清理多余空格
    text = text.replace(/\s+/g, " ").trim();

    if (hasLink) {
        // 链接文本的特殊清理
        // console.log("最终清理链接文本: " + text);

        // 确保链接中的标点符号正确
        text = text.replace(/。/g, ".");  // 中文句号 → 英文点号
        text = text.replace(/，/g, ",");  // 中文逗号 → 英文逗号
        text = text.replace(/：/g, ":");  // 中文冒号 → 英文冒号

        // 修复链接中的空格
        text = text.replace(/\s*\.\s*/g, ".");
        text = text.replace(/\s*:\s*/g, ":");
        text = text.replace(/\s*\/\s*/g, "/");

        // 保护链接单词
        text = protectLinkWords(text);

    } else {
        // 普通文本的标点符号处理
        text = text.replace(/\s+([。！？，、；：])/g, "$1");  // 标点前不要空格
        text = text.replace(/([。！？])\s*([a-zA-Z\u4e00-\u9fa5])/g, "$1 $2"); // 句号后要空格
    }

    // 3. 修复引号
    text = text.replace(/\s*"\s*/g, "\"");
    text = text.replace(/\s*'\s*/g, "'");

    // 4. 确保没有换行符
    text = text.replace(/[\r\n]/g, " ");

    // 5. 最终空格清理
    text = text.replace(/\s+/g, " ").trim();

    return text;
}

/**
 * 微信聊天内容识别函数
 * @returns {Array} 返回识别到的纯净文字数组
 */
function getWeChatMessages() {
    // 检查环境
    if (currentPackage() !== "com.tencent.mm") {
        return [];
    }


    // 执行OCR识别
    var messages = testLinkRecognition();

    // 提取纯净文字
    var cleanTexts = [];
    if (messages && messages.length > 0) {
        for (var i = 0; i < messages.length; i++) {
            if (messages[i] && messages[i].text) {
                cleanTexts.push(messages[i].text);
            }
        }
    }

    return cleanTexts;
}

/**
 * 获取微信聊天内容（字符串格式）
 * @returns {String} 返回所有识别到的文字，用换行符分隔
 */
function getWeChatMessagesAsString() {
    var messages = getWeChatMessages();
    return messages.join('\n');
}

/**
 * 主测试函数（保持向后兼容）
 */
function runLink() {
    sleep(3000);

    var cleanTexts = getWeChatMessages();

    // 输出结果
    for (var i = 0; i < cleanTexts.length; i++) {
        console.log(cleanTexts[i]);
    }
    
    // 阅读完成后关闭控制台
    console.hide();

    return cleanTexts;
}


// 日志模块 - 负责处理日志记录
const LogModule = (function () {
    // 日志级别
    const LOG_LEVEL = {
        DEBUG: 0,
        INFO: 1,
        WARN: 2,
        ERROR: 3
    };

    // 当前日志级别
    let currentLevel = LOG_LEVEL.DEBUG;

    return {
        /**
         * 初始化日志模块
         */
        init: function () {
            console.log("日志模块初始化");
        },

        /**
         * 记录日志
         * @param {string} message - 日志消息
         * @param {string} level - 日志级别，默认为INFO
         */
        log: function (message, level) {
            level = level || "INFO";
            let logLevel = LOG_LEVEL[level] || LOG_LEVEL.INFO;

            // 当前日志级别低于配置的级别则不记录
            if (logLevel < currentLevel) {
                return;
            }

            // 获取当前时间
            let now = new Date();
            let timeString = now.toLocaleTimeString();

            // 格式化日志消息
            let logMessage = "[" + timeString + "][" + level + "] " + message;

            // 打印到控制台
            switch (logLevel) {
                case LOG_LEVEL.ERROR:
                    console.error(logMessage);
                    break;
                case LOG_LEVEL.WARN:
                    console.warn(logMessage);
                    break;
                default:
                    console.log(logMessage);
            }
        },

        /**
         * 设置日志级别
         * @param {string} level - 日志级别
         */
        setLevel: function (level) {
            if (LOG_LEVEL.hasOwnProperty(level)) {
                currentLevel = LOG_LEVEL[level];
                console.log("日志级别已设置为: " + level);
            }
        }
    };
})();

// 存储模块 - 负责处理数据持久化
const StorageModule = (function () {
    // 存储根目录
    const STORAGE_DIR = "/sdcard/脚本助手/storage";

    return {
        /**
         * 初始化存储模块
         */
        init: function () {
            try {
                // 确保存储目录存在
                if (!files.exists(STORAGE_DIR)) {
                    files.createWithDirs(STORAGE_DIR);
                }
                console.log("存储模块初始化完成");
            } catch (e) {
                console.error("存储模块初始化失败: " + e.message);
            }
        },

        /**
         * 保存数据
         * @param {string} key - 键
         * @param {string} value - 值
         * @returns {boolean} 是否成功
         */
        set: function (key, value) {
            try {
                // 使用内部存储
                let storage = storages.create("脚本助手");
                storage.put(key, value);
                return true;
            } catch (e) {
                console.error("保存数据失败: " + e.message);
                return false;
            }
        },

        /**
         * 获取数据
         * @param {string} key - 键
         * @param {string} defaultValue - 默认值
         * @returns {string} 值
         */
        get: function (key, defaultValue) {
            try {
                // 使用内部存储
                let storage = storages.create("脚本助手");
                return storage.get(key, defaultValue);
            } catch (e) {
                console.error("获取数据失败: " + e.message);
                return defaultValue;
            }
        },

        /**
         * 删除数据
         * @param {string} key - 键
         * @returns {boolean} 是否成功
         */
        remove: function (key) {
            try {
                // 使用内部存储
                let storage = storages.create("脚本助手");
                storage.remove(key);
                return true;
            } catch (e) {
                console.error("删除数据失败: " + e.message);
                return false;
            }
        },

        /**
         * 清除所有数据
         * @returns {boolean} 是否成功
         */
        clear: function () {
            try {
                // 使用内部存储
                let storage = storages.create("脚本助手");
                storage.clear();
                return true;
            } catch (e) {
                console.error("清除数据失败: " + e.message);
                return false;
            }
        }
    };
})();
/**
 * 计算两个字符串的相似度（Levenshtein距离）
 * @param {string} str1 - 第一个字符串
 * @param {string} str2 - 第二个字符串
 * @returns {number} 相似度（0-1之间）
 */
function calculateSimilarity(str1, str2) {
    if (!str1 || !str2) return 0;
    if (str1 === str2) return 1;

    const len1 = str1.length;
    const len2 = str2.length;
    const maxLen = Math.max(len1, len2);

    if (maxLen === 0) return 1;

    // 简化的相似度计算
    let matches = 0;
    const minLen = Math.min(len1, len2);

    for (let i = 0; i < minLen; i++) {
        if (str1[i] === str2[i]) {
            matches++;
        }
    }

    return matches / maxLen;
}

// OCR文字识别 + 位置定位方案（优化版本）
function findAndClickSendMessageButtonByOCR(statusChecker) {
    console.log("=== 开始OCR识别发消息按钮 ===");

    // 检查执行状态
    if (!statusChecker.isRunning()) {
        console.log("❌ 功能已停止，取消OCR识别");
        return false;
    }

    // 检查暂停状态
    while (statusChecker.isPaused() && statusChecker.isRunning()) {
        console.log("⏸️ 功能已暂停，等待恢复...");
        sleep(500);
    }

    // 再次检查是否被停止
    if (!statusChecker.isRunning()) {
        console.log("❌ 功能已停止，取消OCR识别");
        return false;
    }

    // 优化：快速权限检查，减少等待时间
    console.log("正在检查截图权限...");
    let testScreenshot = null;
    try {
        testScreenshot = captureScreen();
        if (testScreenshot) {
            console.log("✓ 截图权限已存在");
            testScreenshot.recycle();
        } else {
            throw new Error("需要请求权限");
        }
    } catch (e) {
        console.log("需要请求截图权限...");
        if (!requestScreenCapture()) {
            console.log("❌ 请求截图权限失败");
            return false;
        }
        console.log("✓ 截图权限获取成功");
        // 优化：减少等待时间到500ms
        sleep(500);
    }

    let screenshot = null;
    let regionImage = null;
    let success = false;

    try {
        // 再次检查状态
        if (!statusChecker.isRunning()) {
            console.log("❌ 功能已停止，取消截图");
            return false;
        }

        // 优化：快速截图，减少日志输出
        screenshot = captureScreen();
        if (!screenshot) {
            console.log("❌ 截图失败");
            return false;
        }

        // 优化：精确定位发消息按钮的搜索区域，减少OCR处理范围
        let searchRegion = {
            x: Math.floor(device.width * 0.6),      // 优化：从60%位置开始，更精确
            y: Math.floor(device.height * 0.1),     // 优化：从10%位置开始
            width: Math.floor(device.width * 0.35), // 优化：宽度35%，减少处理范围
            height: Math.floor(device.height * 0.4) // 优化：高度40%
        };

        // 再次检查状态
        if (!statusChecker.isRunning()) {
            console.log("❌ 功能已停止，取消OCR");
            return false;
        }

        // 优化：快速裁剪搜索区域
        regionImage = images.clip(screenshot,
            searchRegion.x,
            searchRegion.y,
            searchRegion.width,
            searchRegion.height
        );

        if (!regionImage) {
            console.log("❌ 图片裁剪失败");
            return false;
        }

        // 优化：图像预处理提高识别准确度
        try {
            // 应用灰度处理提高OCR准确度
            if (typeof images.grayscale === 'function') {
                let grayImage = images.grayscale(regionImage);
                if (grayImage) {
                    regionImage.recycle();
                    regionImage = grayImage;
                }
            }

            // 应用二值化处理进一步提高准确度
            if (typeof images.threshold === 'function') {
                let binaryImage = images.threshold(regionImage, 128, 255, "BINARY");
                if (binaryImage) {
                    regionImage.recycle();
                    regionImage = binaryImage;
                }
            }
        } catch (e) {
            // 图像处理失败时继续使用原图
            console.log("图像预处理失败，使用原图: " + e.message);
        }

        // 优化：使用OCR识别文字
        let ocrResult = null;
        try {
            ocrResult = paddle.ocr(regionImage);
        } catch (ocrError) {
            console.log("❌ OCR识别过程异常: " + ocrError.message);
            return false;
        }

        if (!ocrResult || ocrResult.length === 0) {
            console.log("❌ OCR未识别到任何文字");
            return false;
        }

        // 优化：扩展目标文字匹配，提高识别准确度
        let targetTexts = ["发消息", "发 消息", "发消 息", "发送消息", "发送", "消息"];
        let foundTarget = null;
        let bestMatch = null;
        let bestConfidence = 0;

        // 优化：使用置信度筛选和模糊匹配
        for (let i = 0; i < ocrResult.length; i++) {
            // 检查状态
            if (!statusChecker.isRunning()) {
                console.log("❌ 功能已停止，中断OCR处理");
                return false;
            }

            let item = ocrResult[i];

            // 优化：只处理高置信度的文字（≥0.7）
            if (item.confidence < 0.7) {
                continue;
            }

            // 优化：使用模糊匹配和相似度计算
            for (let targetText of targetTexts) {
                if (item.text && (item.text.includes(targetText) ||
                    targetText.includes(item.text) ||
                    calculateSimilarity(item.text, targetText) > 0.6)) {

                    // 选择置信度最高的匹配
                    if (item.confidence > bestConfidence) {
                        bestMatch = item;
                        bestConfidence = item.confidence;
                        console.log(`✓ 找到更好的匹配: "${item.text}" (置信度: ${item.confidence})`);
                    }
                }
            }
        }

        foundTarget = bestMatch;

        if (foundTarget) {
            // 最后检查状态
            if (!statusChecker.isRunning()) {
                console.log("❌ 功能已停止，取消点击");
                return false;
            }

            // 计算实际点击坐标（加上搜索区域的偏移）
            let bounds = foundTarget.bounds;
            let centerX = searchRegion.x + (bounds.left + bounds.right) / 2;
            let centerY = searchRegion.y + (bounds.top + bounds.bottom) / 2;

            console.log(`✓ 计算点击坐标: (${Math.round(centerX)}, ${Math.round(centerY)})`);
            console.log(`文字置信度: ${foundTarget.confidence}`);

            // 执行点击
            click(centerX, centerY);
            console.log("✓ 已执行点击操作");
            success = true;

        } else {
            console.log("❌ 未找到'发消息'文字");

            // 输出所有识别到的文字供调试
            console.log("所有识别到的文字:");
            for (let i = 0; i < ocrResult.length; i++) {
                console.log(`  ${i}: "${ocrResult[i].text}"`);
            }
        }

    } catch (e) {
        console.log("❌ OCR识别异常: " + e.message);
        console.log("错误堆栈: " + e.stack);
    } finally {
        // 确保释放所有图片资源
        try {
            if (regionImage) {
                regionImage.recycle();
                console.log("✓ 已释放区域图片资源");
            }
        } catch (e) {
            console.log("释放区域图片资源时异常: " + e.message);
        }

        try {
            if (screenshot) {
                screenshot.recycle();
                console.log("✓ 已释放截图资源");
            }
        } catch (e) {
            console.log("释放截图资源时异常: " + e.message);
        }
    }

    console.log("=== OCR识别发消息按钮完成 ===");
    return success;
}
// 月光宝盒模块 - 处理月光宝盒脚本功能
const MoonBoxModule = (function () {
    // 月光宝盒配置
    var moonBoxConfig = {
        function: "read", // 默认选择阅读功能
        quantity: 0,      // 默认数量
        swipeCount: 5,    // 默认滑动次数
        swipeInterval: 1  // 默认滑动间隔（秒）
    };

    // 月光宝盒执行状态
    var moonBoxIsRunning = false;
    var moonBoxIsPaused = false;
    var moonBoxControlWindow = null;
    var moonBoxControlDialog = null;

    // 执行统计数据
    var moonBoxStats = {
        startTime: null,
        currentIndex: 0,
        successCount: 0,
        failCount: 0,
        totalCount: 0
    };

    return {
        /**
         * 创建月光宝盒配置界面
         */
        createMoonBoxUI: function () {
            try {
                console.log("开始创建月光宝盒配置界面");

                // 月光宝盒UI布局
                var moonBoxLayoutXml =
                    '<vertical bg="#f5f5dc">' +
                    '{/* 标题区域 - 带分割线 */}' +
                    '<vertical>' +
                    '<horizontal gravity="center_vertical" h="50">' +
                    '<button id="moonBoxBackBtn" text="返回" textSize="14sp" style="Widget.AppCompat.Button.Borderless" w="60dp" h="40dp"/>' +
                    '<text text="月光宝盒配置" textSize="18sp" textColor="#000000" gravity="center" layout_weight="1" textStyle="bold"/>' +
                    '<frame w="60dp" visibility="invisible"/>' +
                    '</horizontal>' +
                    '<View bg="#cccccc" h="1dp"/>' +
                    '</vertical>' +

                    '{/* 功能区域 - 带分割线 */}' +
                    '<vertical padding="16 20 16 20">' +
                    '<text text="功能" textSize="16sp" textColor="#000000" margin="0 0 16 0"/>' +

                    '{/* 功能选择 - 使用文本控件确保完美对齐 */}' +
                    '<horizontal gravity="center_vertical" padding="0 0 0 0">' +
                    '<text id="moonBoxReadOption"' +
                    'text="● 阅读"' +
                    'textSize="14sp"' +
                    'textColor="#2196f3"' +
                    'padding="8 8 8 8"' +
                    'clickable="true"' +
                    'w="80dp"' +
                    'h="40dp"' +
                    'gravity="center_vertical"/>' +
                    '<text id="moonBoxYouhaoOption"' +
                    'text="○ 优号"' +
                    'textSize="14sp"' +
                    'textColor="#666666"' +
                    'padding="8 8 8 8"' +
                    'clickable="true"' +
                    'w="80dp"' +
                    'h="40dp"' +
                    'gravity="center_vertical"/>' +
                    '<text id="moonBoxShipinhaoOption"' +
                    'text="○ 视频号"' +
                    'textSize="14sp"' +
                    'textColor="#666666"' +
                    'padding="8 8 8 8"' +
                    'clickable="true"' +
                    'w="80dp"' +
                    'h="40dp"' +
                    'gravity="center_vertical"/>' +
                    '</horizontal>' +
                    '</vertical>' +

                    '<View bg="#cccccc" h="1dp"/>' +

                    '{/* 微信限制说明区域 - 默认隐藏 */}' +
                    '<vertical id="moonBoxWechatNotice" padding="16 20 16 20" visibility="gone">' +
                    '<text text="⚠️ 重要提示" textSize="16sp" textColor="#ff6b35" textStyle="bold" margin="0 0 8 0"/>' +
                    '<text text="由于微信限制，暂不支持手机端自动化，请自行下载影刀RPA，向客服索取自动化应用链接。" textSize="14sp" textColor="#666666" margin="0 0 8 0"/>' +
                    '<text id="moonBoxRpaLink" text="影刀RPA下载链接：https://www.yingdao.com/client-download/" textSize="14sp" textColor="#2196f3" clickable="true" margin="0 0 0 0"/>' +
                    '</vertical>' +

                    '<View id="moonBoxNoticeDivider" bg="#cccccc" h="1dp" visibility="gone"/>' +

                    '{/* 配置区域 */}' +
                    '<vertical id="moonBoxConfigArea" padding="16 20 16 20">' +
                    '<text text="配置" textSize="16sp" textColor="#000000" margin="0 0 16 0"/>' +

                    '{/* 数量输入 */}' +
                    '<vertical margin="0 0 0 0">' +
                    '<horizontal gravity="center_vertical" margin="0 0 0 0">' +
                    '<text text="执行数量" textSize="14sp" textColor="#000000" margin="0 0 12 0" w="80dp"/>' +
                    '<input id="moonBoxQuantityInput"' +
                    'hint=""' +
                    'inputType="number"' +
                    'textSize="14sp"' +
                    'w="120dp"' +
                    'h="40dp"' +
                    'bg="#ffffff"' +
                    'text="0"' +
                    'gravity="center"' +
                    'singleLine="true"' +
                    'padding="8"/>' +
                    '</horizontal>' +
                    '<View bg="#e0e0e0" h="1dp" margin="0 20 0 20"/>' +
                    '</vertical>' +

                    '{/* 滑动次数输入 */}' +
                    '<vertical margin="0 0 0 0">' +
                    '<horizontal gravity="center_vertical" margin="0 20 0 0">' +
                    '<text text="滑动次数" textSize="14sp" textColor="#000000" margin="0 0 12 0" w="80dp"/>' +
                    '<input id="moonBoxSwipeCountInput"' +
                    'hint=""' +
                    'inputType="number"' +
                    'textSize="14sp"' +
                    'w="120dp"' +
                    'h="40dp"' +
                    'bg="#ffffff"' +
                    'text="5"' +
                    'gravity="center"' +
                    'singleLine="true"' +
                    'padding="8"/>' +
                    '</horizontal>' +
                    '<View bg="#e0e0e0" h="1dp" margin="0 20 0 20"/>' +
                    '</vertical>' +

                    '{/* 滑动间隔输入 */}' +
                    '<vertical margin="0 0 0 0">' +
                    '<horizontal gravity="center_vertical" margin="0 20 0 0">' +
                    '<text text="滑动间隔(秒)" textSize="14sp" textColor="#000000" margin="0 0 12 0" w="80dp"/>' +
                    '<input id="moonBoxSwipeIntervalInput"' +
                    'hint=""' +
                    'inputType="number"' +
                    'textSize="14sp"' +
                    'w="120dp"' +
                    'h="40dp"' +
                    'bg="#ffffff"' +
                    'text="1"' +
                    'gravity="center"' +
                    'singleLine="true"' +
                    'padding="8"/>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</vertical>' +

                    '{/* 底部空白区域 */}' +
                    '<vertical layout_weight="1"/>' +

                    '{/* 启动和停止按钮 - 底部固定 */}' +
                    '<vertical padding="20 20 20 30">' +
                    '<horizontal gravity="center" layout_gravity="center">' +
                    '<frame layout_weight="1" gravity="center">' +
                    '<button id="moonBoxStartBtn"' +
                    'text="启动"' +
                    'textSize="16sp"' +
                    'textColor="#ffffff"' +
                    'bg="#8e44ad"' +
                    'w="120dp"' +
                    'h="45dp"' +
                    'style="Widget.AppCompat.Button"/>' +
                    '</frame>' +
                    '<frame layout_weight="1" gravity="center">' +
                    '<button id="moonBoxStopBtn"' +
                    'text="停止"' +
                    'textSize="16sp"' +
                    'textColor="#ffffff"' +
                    'bg="#e74c3c"' +
                    'w="120dp"' +
                    'h="45dp"' +
                    'style="Widget.AppCompat.Button"/>' +
                    '</frame>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</vertical>';

                // 使用安全的UI创建方法
                UIManager.safeCreateUI(moonBoxLayoutXml, function () {
                    try {
                        console.log("月光宝盒界面创建完成，开始绑定事件");

                        // 设置当前页面状态
                        UIModule.setCurrentPage("moonBox");

                        // 绑定功能选择事件
                        console.log("开始绑定功能选择事件");
                        try {
                            MoonBoxModule.bindFunctionSelectionEvents();
                            console.log("功能选择事件绑定完成");
                        } catch (e) {
                            console.error("绑定功能选择事件失败: " + e.message);
                        }

                        // 绑定数量输入事件
                        console.log("开始绑定数量输入事件");
                        try {
                            MoonBoxModule.bindQuantityInputEvent();
                            console.log("数量输入事件绑定完成");
                        } catch (e) {
                            console.error("绑定数量输入事件失败: " + e.message);
                        }

                        // 绑定启动按钮事件
                        console.log("开始绑定启动按钮事件");
                        try {
                            MoonBoxModule.bindStartButtonEvent();
                            console.log("启动按钮事件绑定完成");
                        } catch (e) {
                            console.error("绑定启动按钮事件失败: " + e.message);
                        }

                        // 绑定影刀RPA链接点击事件
                        console.log("开始绑定影刀RPA链接事件");
                        try {
                            MoonBoxModule.bindRpaLinkEvent();
                            console.log("影刀RPA链接事件绑定完成");
                        } catch (e) {
                            console.error("绑定影刀RPA链接事件失败: " + e.message);
                        }

                        // 绑定停止按钮事件
                        console.log("开始绑定停止按钮事件");
                        try {
                            MoonBoxModule.bindStopButtonEvent();
                            console.log("停止按钮事件绑定完成");
                        } catch (e) {
                            console.error("绑定停止按钮事件失败: " + e.message);
                        }

                        // 绑定返回按钮事件
                        console.log("开始绑定返回按钮事件");
                        try {
                            MoonBoxModule.bindBackButtonEvent();
                            console.log("返回按钮事件绑定完成");
                        } catch (e) {
                            console.error("绑定返回按钮事件失败: " + e.message);
                        }

                        console.log("月光宝盒界面所有事件绑定完成");
                    } catch (e) {
                        console.error("绑定月光宝盒界面事件失败: " + e.message);
                        console.error("异常堆栈: " + e.stack);
                    }
                });
            } catch (e) {
                console.error("创建月光宝盒界面失败: " + e.message);
            }
        },

        /**
         * 绑定功能选择事件
         */
        bindFunctionSelectionEvents: function () {
            try {
                // 功能选择函数
                function selectMoonBoxFunction(selectedFunction, buttonName) {
                    // 重置所有按钮为未选中状态
                    ui.moonBoxReadOption.setText("○ 阅读");
                    ui.moonBoxReadOption.setTextColor(colors.parseColor("#666666"));

                    ui.moonBoxYouhaoOption.setText("○ 优号");
                    ui.moonBoxYouhaoOption.setTextColor(colors.parseColor("#666666"));

                    ui.moonBoxShipinhaoOption.setText("○ 视频号");
                    ui.moonBoxShipinhaoOption.setTextColor(colors.parseColor("#666666"));

                    // 设置选中的按钮
                    switch (selectedFunction) {
                        case "read":
                            ui.moonBoxReadOption.setText("● 阅读");
                            ui.moonBoxReadOption.setTextColor(colors.parseColor("#2196f3"));
                            // 隐藏微信限制说明
                            ui.moonBoxWechatNotice.setVisibility(8); // View.GONE
                            ui.moonBoxNoticeDivider.setVisibility(8); // View.GONE
                            // 显示配置区域
                            ui.moonBoxConfigArea.setVisibility(0); // View.VISIBLE
                            // 显示启动和停止按钮
                            ui.moonBoxStartBtn.setVisibility(0); // View.VISIBLE
                            ui.moonBoxStopBtn.setVisibility(0); // View.VISIBLE
                            break;
                        case "youhao":
                            ui.moonBoxYouhaoOption.setText("● 优号");
                            ui.moonBoxYouhaoOption.setTextColor(colors.parseColor("#2196f3"));
                            // 显示微信限制说明
                            ui.moonBoxWechatNotice.setVisibility(0); // View.VISIBLE
                            ui.moonBoxNoticeDivider.setVisibility(0); // View.VISIBLE
                            // 隐藏配置区域
                            ui.moonBoxConfigArea.setVisibility(8); // View.GONE
                            // 隐藏启动和停止按钮
                            ui.moonBoxStartBtn.setVisibility(8); // View.GONE
                            ui.moonBoxStopBtn.setVisibility(8); // View.GONE
                            break;
                        case "shipinhao":
                            ui.moonBoxShipinhaoOption.setText("● 视频号");
                            ui.moonBoxShipinhaoOption.setTextColor(colors.parseColor("#2196f3"));
                            // 显示微信限制说明
                            ui.moonBoxWechatNotice.setVisibility(0); // View.VISIBLE
                            ui.moonBoxNoticeDivider.setVisibility(0); // View.VISIBLE
                            // 隐藏配置区域
                            ui.moonBoxConfigArea.setVisibility(8); // View.GONE
                            // 隐藏启动和停止按钮
                            ui.moonBoxStartBtn.setVisibility(8); // View.GONE
                            ui.moonBoxStopBtn.setVisibility(8); // View.GONE
                            break;
                    }

                    moonBoxConfig.function = selectedFunction;
                    console.log("选择功能: " + buttonName);
                    toast("已选择: " + buttonName);
                }

                // 绑定点击事件
                ui.moonBoxReadOption.on("click", function () {
                    selectMoonBoxFunction("read", "阅读");
                });

                ui.moonBoxYouhaoOption.on("click", function () {
                    selectMoonBoxFunction("youhao", "优号");
                });

                ui.moonBoxShipinhaoOption.on("click", function () {
                    selectMoonBoxFunction("shipinhao", "视频号");
                });

                console.log("月光宝盒功能选择事件绑定完成");
            } catch (e) {
                console.error("绑定月光宝盒功能选择事件失败: " + e.message);
            }
        },

        /**
         * 绑定数量输入事件
         */
        bindQuantityInputEvent: function () {
            try {
                // 绑定执行数量输入事件
                ui.moonBoxQuantityInput.on("text_changed", function (text) {
                    var num = parseInt(text);
                    if (!isNaN(num) && num > 0) {
                        moonBoxConfig.quantity = num;
                        console.log("设置数量: " + moonBoxConfig.quantity);
                    }
                });

                // 绑定滑动次数输入事件
                ui.moonBoxSwipeCountInput.on("text_changed", function (text) {
                    var num = parseInt(text);
                    if (!isNaN(num) && num > 0) {
                        moonBoxConfig.swipeCount = num;
                        console.log("设置滑动次数: " + moonBoxConfig.swipeCount);
                    }
                });

                // 绑定滑动间隔输入事件
                ui.moonBoxSwipeIntervalInput.on("text_changed", function (text) {
                    var num = parseInt(text);
                    if (!isNaN(num) && num > 0) {
                        moonBoxConfig.swipeInterval = num;
                        console.log("设置滑动间隔: " + moonBoxConfig.swipeInterval + "秒");
                    }
                });

                console.log("月光宝盒输入事件绑定完成");
            } catch (e) {
                console.error("绑定月光宝盒输入事件失败: " + e.message);
            }
        },

        /**
         * 绑定影刀RPA链接点击事件
         */
        bindRpaLinkEvent: function () {
            try {
                ui.moonBoxRpaLink.on("click", function () {
                    try {
                        var rpaUrl = "https://www.yingdao.com/client-download/";

                        // 显示选择对话框
                        dialogs.select("影刀RPA下载", ["复制链接", "打开浏览器"])
                            .then(function (index) {
                                if (index === 0) {
                                    // 复制链接到剪贴板
                                    setClip(rpaUrl);
                                    toast("链接已复制到剪贴板");
                                } else if (index === 1) {
                                    // 打开浏览器
                                    try {
                                        app.openUrl(rpaUrl);
                                        toast("正在打开浏览器...");
                                    } catch (e) {
                                        console.error("打开浏览器失败: " + e.message);
                                        // 如果打开浏览器失败，则复制链接
                                        setClip(rpaUrl);
                                        toast("无法打开浏览器，链接已复制到剪贴板");
                                    }
                                }
                            })
                            .catch(function (e) {
                                console.error("显示选择对话框失败: " + e.message);
                            });
                    } catch (e) {
                        console.error("处理RPA链接点击失败: " + e.message);
                        toast("操作失败");
                    }
                });

                console.log("影刀RPA链接事件绑定完成");
            } catch (e) {
                console.error("绑定影刀RPA链接事件失败: " + e.message);
            }
        },

        /**
         * 绑定启动按钮事件
         */
        bindStartButtonEvent: function () {
            try {
                console.log("开始绑定启动按钮事件");

                // 检查UI元素是否存在
                if (!ui.moonBoxStartBtn) {
                    console.error("moonBoxStartBtn UI元素不存在");
                    return;
                }

                if (!ui.moonBoxQuantityInput) {
                    console.error("moonBoxQuantityInput UI元素不存在");
                    return;
                }

                console.log("UI元素检查通过，开始绑定点击事件");

                ui.moonBoxStartBtn.on("click", function () {
                    console.log("=== 启动按钮被点击 ===");

                    try {

                        // 获取并验证数量
                        console.log("准备获取配置参数");
                        var quantity = parseInt(ui.moonBoxQuantityInput.text());
                        var swipeCount = parseInt(ui.moonBoxSwipeCountInput.text());
                        var swipeInterval = parseInt(ui.moonBoxSwipeIntervalInput.text());

                        console.log("获取到的配置 - 数量: " + quantity + ", 滑动次数: " + swipeCount + ", 滑动间隔: " + swipeInterval);

                        if (isNaN(quantity) || quantity <= 0) {
                            toast("请输入有效的执行数量！");
                            console.log("数量验证失败");
                            return;
                        }

                        if (isNaN(swipeCount) || swipeCount <= 0) {
                            toast("请输入有效的滑动次数！");
                            console.log("滑动次数验证失败");
                            return;
                        }

                        if (isNaN(swipeInterval) || swipeInterval <= 0) {
                            toast("请输入有效的滑动间隔！");
                            console.log("滑动间隔验证失败");
                            return;
                        }

                        console.log("所有配置验证通过");

                        // 设置配置
                        moonBoxConfig.quantity = quantity;
                        moonBoxConfig.swipeCount = swipeCount;
                        moonBoxConfig.swipeInterval = swipeInterval;
                        console.log("配置已设置 - 数量: " + moonBoxConfig.quantity + ", 滑动次数: " + moonBoxConfig.swipeCount + ", 滑动间隔: " + moonBoxConfig.swipeInterval + "秒");

                        // 检查功能选择
                        if (!moonBoxConfig.function) {
                            console.log("未选择功能，显示提示");
                            toast("请先选择要执行的功能");
                            return;
                        }

                        // 检查是否已在运行
                        if (moonBoxIsRunning) {
                            toast("功能正在执行中");
                            console.log("功能已在运行，退出");
                            return;
                        }

                        console.log("步骤7: 检测微信界面");
                        // 检测是否在微信界面
                        if (currentPackage() !== "com.tencent.mm") {
                            console.log("当前不在微信界面，提醒用户切换");
                            toast("请切换到微信并进入阅光宝盒界面，5秒后自动开始执行");

                            // 使用setTimeout代替sleep，避免阻塞UI线程
                            setTimeout(function () {
                                console.log("开始自动执行功能");
                                toast("🚀 开始执行 " + moonBoxConfig.function + " 功能");

                                // 直接开始执行功能
                                MoonBoxModule.startExecution();

                                console.log("🚀 月光宝盒功能已启动");
                                console.log("📊 启动流程完成");
                            }, 5000);
                            return;
                        } else {
                            console.log("✓ 已在微信界面");
                            console.log("开始自动执行功能");
                            toast("🚀 开始执行 " + moonBoxConfig.function + " 功能");

                            // 直接开始执行功能
                            MoonBoxModule.startExecution();
                        }

                        console.log("🚀 月光宝盒功能已启动");
                        console.log("📊 启动流程完成");
                        return;
                    } catch (e) {
                        console.error("启动按钮处理异常: " + e.message);
                        console.error("异常堆栈: " + e.stack);
                        return;
                    }
                });

                console.log("月光宝盒启动按钮事件绑定完成");
            } catch (e) {
                console.error("绑定月光宝盒启动按钮事件失败: " + e.message);
                console.error("异常堆栈: " + e.stack);
            }
        },

        /**
         * 绑定停止按钮事件
         */
        bindStopButtonEvent: function () {
            try {
                console.log("开始绑定停止按钮事件");

                // 检查UI元素是否存在
                if (!ui.moonBoxStopBtn) {
                    console.error("moonBoxStopBtn UI元素不存在");
                    return;
                }

                console.log("停止按钮UI元素检查通过，开始绑定点击事件");

                ui.moonBoxStopBtn.on("click", function () {
                    console.log("=== 停止按钮被点击 ===");

                    try {
                        if (moonBoxIsRunning) {
                            console.log("正在停止月光宝盒功能");
                            moonBoxIsRunning = false;
                            moonBoxIsPaused = false;
                            toast("⏹️ 已停止执行");
                            console.log("⏹️ 执行已停止");

                            // 显示统计数据
                            MoonBoxModule.showStatisticsDialog("停止");
                        } else {
                            console.log("功能未在运行");
                            toast("功能未在运行");
                        }
                    } catch (e) {
                        console.error("停止按钮处理异常: " + e.message);
                        console.error("异常堆栈: " + e.stack);
                        toast("停止操作失败");
                    }
                });

                console.log("月光宝盒停止按钮事件绑定完成");
            } catch (e) {
                console.error("绑定月光宝盒停止按钮事件失败: " + e.message);
                console.error("异常堆栈: " + e.stack);
            }
        },

        /**
         * 创建控制面板（已移除悬浮窗控制面板）
         */
        createControlPanel: function () {
            try {
                console.log("控制面板功能已移除，使用页面按钮控制");
                // 不再创建悬浮窗控制面板，改为使用页面上的启动和停止按钮
            } catch (e) {
                console.error("控制面板功能已移除: " + e.message);
            }
        },

        /**
         * 设置拖拽功能（已移除）
         */
        setupDragFunction: function () {
            // 控制面板已移除，此功能不再需要
        },

        /**
         * 绑定控制面板事件（已移除）
         */
        bindControlPanelEvents: function () {
            // 控制面板已移除，此功能不再需要
        },

        /**
         * 更新控制面板状态（已移除）
         */
        updateControlPanelStatus: function () {
            // 控制面板已移除，此功能不再需要
        },

        /**
         * 显示统计数据对话框
         */
        showStatisticsDialog: function (action) {
            try {
                let currentTime = new Date();
                let duration = moonBoxStats.startTime ?
                    Math.round((currentTime - moonBoxStats.startTime) / 1000) : 0;

                let successRate = moonBoxStats.totalCount > 0 ?
                    Math.round((moonBoxStats.successCount / moonBoxStats.totalCount) * 100) : 0;

                let statusIcon = action === "暂停" ? "⏸️" : (action === "完成" ? "🎉" : "⏹️");
                let statusText = action === "暂停" ? "执行已暂停" : (action === "完成" ? "执行已完成" : "执行已停止");

                let content = statusIcon + " " + statusText + "\n\n" +
                    "📊 执行统计:\n" +
                    "• 功能: " + (moonBoxConfig.function || "未知") + "\n" +
                    "• 目标数量: " + moonBoxConfig.quantity + "\n" +
                    "• 当前进度: " + moonBoxStats.currentIndex + "/" + moonBoxConfig.quantity + "\n" +
                    "• 成功次数: " + moonBoxStats.successCount + "\n" +
                    "• 失败次数: " + moonBoxStats.failCount + "\n" +
                    "• 成功率: " + successRate + "%\n" +
                    "• 执行时间: " + duration + "秒\n" +
                    "• 操作时间: " + currentTime.toLocaleTimeString();

                let dialogOptions = {
                    title: "🌙 月光宝盒 - 执行统计",
                    content: content,
                    positive: "确定",
                    cancelable: true
                };

                if (action === "暂停") {
                    dialogOptions.negative = "继续执行";
                    dialogOptions.neutral = "停止执行";
                } else if (action === "完成") {
                    dialogOptions.negative = "查看记录";
                    dialogOptions.neutral = "关闭控制台";
                }

                let dialog = dialogs.build(dialogOptions)
                    .on("positive", () => {
                        // 确定按钮
                        console.log("用户确认统计信息");
                    });

                if (action === "暂停") {
                    dialog.on("negative", () => {
                        // 继续执行
                        moonBoxIsPaused = false;
                        toast("⏯️ 继续执行");
                    })
                        .on("neutral", () => {
                            // 停止执行
                            moonBoxIsRunning = false;
                            moonBoxIsPaused = false;
                            toast("⏹️ 已停止执行");

                            // 关闭控制面板
                            setTimeout(function () {
                                if (moonBoxControlWindow) {
                                    moonBoxControlWindow.close();
                                    moonBoxControlWindow = null;
                                }
                            }, 2000);
                        });
                } else if (action === "完成") {
                    dialog.on("negative", () => {
                        // 查看记录
                        MoonBoxModule.showRecordsDialog();
                    })
                        .on("neutral", () => {
                            // 关闭控制台
                            toast("关闭控制台");

                            // 关闭控制面板
                            setTimeout(function () {
                                if (moonBoxControlWindow) {
                                    moonBoxControlWindow.close();
                                    moonBoxControlWindow = null;
                                }
                            }, 1000);
                        });
                }

                dialog.show();
            } catch (e) {
                console.error("显示统计对话框失败: " + e.message);
                toast("显示统计信息失败");
            }
        },

        /**
         * 重置统计数据
         */
        resetStats: function () {
            moonBoxStats.startTime = new Date();
            moonBoxStats.currentIndex = 0;
            moonBoxStats.successCount = 0;
            moonBoxStats.failCount = 0;
            moonBoxStats.totalCount = 0;
        },

        /**
         * 更新统计数据
         */
        updateStats: function (index, success) {
            moonBoxStats.currentIndex = index;
            moonBoxStats.totalCount = index;
            if (success) {
                moonBoxStats.successCount++;
            } else {
                moonBoxStats.failCount++;
            }

            // 实时更新控制台进度显示（控制面板已移除）
        },

        /**
         * 显示简化的控制对话框（备用方案）
         */
        showSimpleControlDialog: function () {
            try {
                console.log("显示持久控制界面");

                let dialogContent = "🌙 月光宝盒控制台\n\n" +
                    "📋 当前配置:\n" +
                    "• 功能: " + (moonBoxConfig.function || "未选择") + "\n" +
                    "• 数量: " + moonBoxConfig.quantity + "\n" +
                    "• 滑动次数: " + moonBoxConfig.swipeCount + "\n" +
                    "• 滑动间隔: " + moonBoxConfig.swipeInterval + "秒\n" +
                    "• 状态: " + (moonBoxIsRunning ? "运行中" : "待机") + "\n" +
                    "• 时间: " + new Date().toLocaleTimeString() + "\n\n" +
                    "💡 提示: 执行日志会输出到控制台";

                // 创建持久的控制对话框
                let controlDialog = dialogs.build({
                    title: "🌙 月光宝盒控制台",
                    content: dialogContent,
                    positive: moonBoxIsRunning ? (moonBoxIsPaused ? "继续" : "暂停") : "开始",
                    negative: "停止",
                    neutral: "更多",
                    cancelable: false  // 不允许取消，必须通过按钮操作
                })
                    .on("positive", () => {
                        if (!moonBoxIsRunning) {
                            // 开始执行
                            console.log("用户点击开始执行");
                            MoonBoxModule.startExecution();
                            // 更新对话框状态
                            MoonBoxModule.updateControlDialog(controlDialog);
                        } else if (moonBoxIsPaused) {
                            // 继续执行
                            console.log("用户点击继续执行");
                            moonBoxIsPaused = false;
                            toast("继续执行");
                            MoonBoxModule.updateControlDialog(controlDialog);
                        } else {
                            // 暂停执行
                            console.log("用户点击暂停执行");
                            moonBoxIsPaused = true;
                            toast("已暂停");
                            MoonBoxModule.updateControlDialog(controlDialog);
                        }
                    })
                    .on("negative", () => {
                        // 停止执行
                        console.log("用户点击停止执行");
                        moonBoxIsRunning = false;
                        moonBoxIsPaused = false;
                        toast("已停止执行");
                        controlDialog.dismiss();
                    })
                    .on("neutral", () => {
                        // 更多选项
                        MoonBoxModule.showMoreOptionsDialog(controlDialog);
                    });

                controlDialog.show();

                // 保存对话框引用，用于更新状态
                moonBoxControlDialog = controlDialog;

                console.log("持久控制界面显示完成");
            } catch (e) {
                console.error("显示控制界面失败: " + e.message);
                toast("显示控制界面失败: " + e.message);
            }
        },

        /**
         * 更新控制对话框状态
         */
        updateControlDialog: function (dialog) {
            try {
                let dialogContent = "🌙 月光宝盒控制台\n\n" +
                    "📋 当前配置:\n" +
                    "• 功能: " + (moonBoxConfig.function || "未选择") + "\n" +
                    "• 数量: " + moonBoxConfig.quantity + "\n" +
                    "• 滑动次数: " + moonBoxConfig.swipeCount + "\n" +
                    "• 滑动间隔: " + moonBoxConfig.swipeInterval + "秒\n" +
                    "• 状态: " + (moonBoxIsRunning ? (moonBoxIsPaused ? "已暂停" : "运行中") : "待机") + "\n" +
                    "• 时间: " + new Date().toLocaleTimeString() + "\n\n" +
                    "💡 提示: 执行日志会输出到控制台";

                // 更新对话框内容和按钮文本
                ui.run(() => {
                    if (dialog && dialog.getContentView) {
                        // 更新按钮文本
                        let positiveButton = dialog.getButton(dialogs.BUTTON_POSITIVE);
                        if (positiveButton) {
                            positiveButton.setText(moonBoxIsRunning ? (moonBoxIsPaused ? "继续" : "暂停") : "开始");
                        }
                    }
                });
            } catch (e) {
                console.error("更新控制对话框失败: " + e.message);
            }
        },

        /**
         * 显示更多选项对话框
         */
        showMoreOptionsDialog: function (parentDialog) {
            try {
                dialogs.build({
                    title: "更多选项",
                    items: ["📊 查看记录", "🖥️ 显示控制台", "🔄 刷新状态", "❌ 关闭控制台"],
                    cancelable: true
                })
                    .on("choice", (index) => {
                        switch (index) {
                            case 0: // 查看记录
                                console.log("用户查看记录");
                                MoonBoxModule.showRecordsDialog();
                                break;
                            case 1: // 显示控制台
                                console.log("用户手动显示控制台");
                                try {
                                    console.show();
                                    toast("控制台已显示，可查看详细日志");
                                } catch (e) {
                                    console.error("显示控制台失败: " + e.message);
                                    toast("显示控制台失败: " + e.message);
                                }
                                break;
                            case 2: // 刷新状态
                                console.log("用户刷新状态");
                                MoonBoxModule.updateControlDialog(parentDialog);
                                toast("状态已刷新");
                                break;
                            case 3: // 关闭控制台
                                console.log("用户关闭控制台");
                                moonBoxIsRunning = false;
                                moonBoxIsPaused = false;
                                if (parentDialog) {
                                    parentDialog.dismiss();
                                }
                                toast("控制台已关闭");
                                break;
                        }
                    })
                    .show();
            } catch (e) {
                console.error("显示更多选项对话框失败: " + e.message);
                toast("显示更多选项失败: " + e.message);
            }
        },

        /**
         * 开始执行功能
         */
        startExecution: function () {
            try {
                console.log("========== 开始执行月光宝盒功能 ==========");
                console.log("功能类型: " + moonBoxConfig.function);
                console.log("执行数量: " + moonBoxConfig.quantity);
                console.log("执行时间: " + new Date().toLocaleString());

                if (!moonBoxConfig.function) {
                    toast("请先选择要执行的功能");
                    console.log("❌ 未选择功能，执行终止");
                    return;
                }

                // 重置并初始化统计数据
                MoonBoxModule.resetStats();

                moonBoxIsRunning = true;
                moonBoxIsPaused = false;
                toast("🚀 开始执行 " + moonBoxConfig.function + " 功能");
                console.log("✅ 开始执行，状态已设置为运行中");

                // 使用线程执行功能，避免阻塞UI
                threads.start(function () {
                    try {
                        // 根据功能类型执行相应操作
                        switch (moonBoxConfig.function) {
                            case "read":
                                console.log("📖 准备执行阅读功能");
                                // 显示控制台以便查看执行步骤
                                threads.start(function () {
                                    console.show();
                                });
                                console.log("🖥️ 控制台已显示，可查看详细执行步骤");
                                MoonBoxModule.executeRead();
                                break;
                            case "youhao":
                                console.log("👤 准备执行优号功能");
                                MoonBoxModule.executeYouhao();
                                break;
                            case "shipinhao":
                                console.log("📹 准备执行视频号功能");
                                MoonBoxModule.executeShipinhao();
                                break;
                            default:
                                console.log("❌ 未知的功能类型: " + moonBoxConfig.function);
                                ui.run(() => toast("未知的功能类型: " + moonBoxConfig.function));
                                moonBoxIsRunning = false;
                                break;
                        }
                    } catch (e) {
                        console.error("❌ 执行功能失败: " + e.message);
                        console.error("异常堆栈: " + e.stack);
                        ui.run(() => toast("执行功能失败: " + e.message));
                        moonBoxIsRunning = false;
                    } finally {
                        // 执行完成后更新状态
                        moonBoxIsRunning = false;
                        moonBoxIsPaused = false;
                        console.log("🏁 执行完成，状态已重置");
                        ui.run(() => {
                            toast("执行完成");
                        });
                    }
                });
            } catch (e) {
                console.error("❌ 启动执行失败: " + e.message);
                console.error("异常堆栈: " + e.stack);
                toast("启动执行失败: " + e.message);
                moonBoxIsRunning = false;
            }
        },

        /**
         * 显示记录对话框
         */
        showRecordsDialog: function () {
            try {
                let records = MoonBoxModule.getReadRecords();
                let content = "阅读记录 (共" + records.length + "条):\n\n";

                if (records.length === 0) {
                    content += "暂无记录";
                } else {
                    records.slice(-10).forEach((record, index) => {
                        content += (index + 1) + ". " + record.timestamp +
                            " - 阅读量: " + (record.readCount === -1 ? "未知" : record.readCount) + "\n";
                    });
                }

                dialogs.build({
                    title: "📊 执行记录",
                    content: content,
                    positive: "确定",
                    negative: "清除记录",
                    cancelable: true
                })
                    .on("negative", () => {
                        MoonBoxModule.clearReadRecords();
                    })
                    .show();
            } catch (e) {
                console.error("显示记录对话框失败: " + e.message);
                toast("显示记录失败: " + e.message);
            }
        },

        /**
         * 绑定返回按钮事件
         */
        bindBackButtonEvent: function () {
            try {
                ui.moonBoxBackBtn.on("click", function () {
                    console.log("月光宝盒返回按钮被点击");
                    // 返回到脚本中心
                    UIModule.createScriptCenterUI(false);
                });

                console.log("月光宝盒返回按钮事件绑定完成");
            } catch (e) {
                console.error("绑定月光宝盒返回按钮事件失败: " + e.message);
            }
        },



        /**
         * 创建悬浮控制窗口（已移除）
         */
        createControlWindow: function () {
            // 控制窗口功能已移除，使用页面按钮控制
        },

        /**
         * 绑定控制窗口事件（已移除）
         */
        bindControlWindowEvents: function () {
            // 控制窗口功能已移除，使用页面按钮控制
        },

        /**
         * 执行阅读功能
         */
        executeRead: function () {
            try {
                console.info("🚀 月光宝盒阅读功能启动");
                console.warn("📊 用户配置信息:");
                console.warn("   执行数量: " + moonBoxConfig.quantity + " 次");
                console.warn("   滑动次数: " + moonBoxConfig.swipeCount + " 次");
                console.warn("   滑动间隔: " + moonBoxConfig.swipeInterval + " 秒");



                ui.run(function () {
                    toast("正在执行阅读功能...");
                });

                let successCount = 0;
                let failCount = 0;
                let startTime = new Date();
                
                // 执行阅读逻辑
                for (let i = 0; i < moonBoxConfig.quantity && moonBoxIsRunning; i++) {
                    console.info("🔄 当前进度: " + (i + 1) + "/" + moonBoxConfig.quantity);

                    // 更新当前进度到统计数据（开始执行时）
                    moonBoxStats.currentIndex = i + 1;
                    ui.run(function () {
                        // 实时更新控制台进度（控制面板已移除）
                    });

                    // 检查是否被暂停
                    while (moonBoxIsPaused && moonBoxIsRunning) {
                        // 暂停时也要保持进度显示更新（控制面板已移除）
                        ui.run(function () {
                            // 控制面板已移除
                        });
                        sleep(500);
                    }

                    // 检查是否被停止
                    if (!moonBoxIsRunning) {
                        break;
                    }

                    ui.run(function () {
                        toast("执行第 " + (i + 1) + "/" + moonBoxConfig.quantity + " 次阅读操作");
                    });

                    // 执行阅读操作
                    let readResult = MoonBoxModule.performReadOperation(i + 1);

                    if (readResult) {
                        successCount++;
                        ui.run(function () {
                            toast("✅ 第 " + (i + 1) + " 次成功");
                        });
                    } else {
                        failCount++;
                        ui.run(function () {
                            toast("❌ 第 " + (i + 1) + " 次失败");
                        });
                    }

                    // 更新统计数据
                    MoonBoxModule.updateStats(i + 1, readResult);

                    // 操作间隔
                    if (i < moonBoxConfig.quantity - 1 && moonBoxIsRunning) {
                        sleep(2000);
                    }
                }

                // 执行完成统计
                let endTime = new Date();
                let duration = Math.round((endTime - startTime) / 1000);
                let successRate = (successCount > 0 ? Math.round((successCount / (successCount + failCount)) * 100) : 0);

                console.info("🏁 执行完成");
                console.warn("📈 最终统计: 成功 " + successCount + " 次，失败 " + failCount + " 次");

                if (moonBoxIsRunning) {
                    // 更新最终进度显示
                    ui.run(function () {
                        toast("🎉 阅读功能执行完成！成功:" + successCount + " 失败:" + failCount);
                        // 确保最终进度显示正确（控制面板已移除）
                    });
                    console.log("🎉 所有操作已完成！");

                    // 显示最终统计
                    setTimeout(function () {
                        MoonBoxModule.showStatisticsDialog("完成");
                    }, 1000);
                } else {
                    ui.run(function () {
                        toast("⏹️ 执行被手动停止");
                        // 停止时也更新进度显示（控制面板已移除）
                    });
                    console.log("⏹️ 执行被用户手动停止");
                }

                console.log("========================================\n");
                
                // 阅读功能执行完毕后关闭控制台
                setTimeout(function() {
                    console.hide();
                }, 2000); // 延迟2秒后关闭控制台，让用户看到最终结果
                
            } catch (e) {
                console.error("❌ 执行阅读功能时发生严重异常: " + e.message);
                console.error("异常堆栈: " + e.stack);
                ui.run(function () {
                    toast("阅读功能执行异常: " + e.message);
                });
                
                // 即使发生异常也关闭控制台
                setTimeout(function() {
                    console.hide();
                }, 2000);
            }
        },

        /**
         * 执行优号功能 - 重构版本
         */
        executeYouhao: function () {
            try {
                console.log("=== 开始执行优号功能 ===");
                console.log("执行数量: " + moonBoxConfig.quantity);
                ui.run(function () {
                    toast("正在执行优号功能...");
                });

                // 步骤2：创建记录执行失败的公众号的数组
                let failedAccounts = [];
                console.log("✓ 步骤2：创建失败公众号记录数组");

                // 主循环：根据用户设置的执行数量执行
                for (let i = 0; i < moonBoxConfig.quantity && moonBoxIsRunning; i++) {
                    // 检查是否被暂停
                    while (moonBoxIsPaused && moonBoxIsRunning) {
                        sleep(500);
                    }

                    // 检查是否被停止
                    if (!moonBoxIsRunning) {
                        break;
                    }

                    console.log("=== 执行第 " + (i + 1) + " 次优号操作 ===");
                    ui.run(function () {
                        toast("执行第 " + (i + 1) + " 次优号操作");
                    });

                    // 执行单次优号操作
                    let success = MoonBoxModule.performSingleYouhaoOperation(failedAccounts);

                    if (!success && !moonBoxIsRunning) {
                        console.log("操作被中断，停止执行");
                        break;
                    }

                    // 短暂休息后继续下一次操作
                    sleep(1000);
                }

                if (moonBoxIsRunning) {
                    ui.run(function () {
                        toast("优号功能执行完成！");
                    });
                    console.log("=== 优号功能执行完成 ===");
                }
                
                // 优号功能执行完毕后关闭控制台
                setTimeout(function() {
                    console.hide();
                }, 2000); // 延迟2秒后关闭控制台，让用户看到最终结果
                
            } catch (e) {
                console.error("执行优号功能失败: " + e.message);
                console.error("异常堆栈: " + e.stack);
                ui.run(function () {
                    toast("优号功能执行异常: " + e.message);
                });
                
                // 即使发生异常也关闭控制台
                setTimeout(function() {
                    console.hide();
                }, 2000);
            }
        },

        /**
         * 执行单次优号操作
         * @param {Array} failedAccounts - 失败公众号记录数组
         * @returns {boolean} 是否成功
         */
        performSingleYouhaoOperation: function (failedAccounts) {
            let retryCount = 0;
            let maxRetries = 3;
            let currentAccountName = ""; // 用于记录当前公众号名称

            while (retryCount < maxRetries && moonBoxIsRunning) {
                try {
                    console.log("--- 开始单次优号操作 (重试次数: " + retryCount + ") ---");

                    // 步骤3：点击"优号按钮"
                    if (!MoonBoxModule.clickYouhaoButton(retryCount)) {
                        retryCount++;
                        if (retryCount >= maxRetries) {
                            console.log("❌ 重试3次后仍无法找到优号按钮，结束并提醒用户");
                            ui.run(() => toast("微信限制，等待脚本更新"));
                            return false;
                        }
                        continue;
                    }

                    // 步骤4：获取所有粉Ta按钮元素并随机选择一个进行点击
                    let selectedFentaButton = MoonBoxModule.getAndClickRandomFentaButton(retryCount);
                    if (!selectedFentaButton) {
                        retryCount++;
                        if (retryCount >= maxRetries) {
                            console.log("❌ 重试3次后仍无法找到粉Ta按钮，结束并提醒用户");
                            ui.run(() => toast("微信限制，等待脚本更新"));
                            return false;
                        }
                        continue;
                    }

                    // 步骤5：查找优选文章入口并点击进入优选文章页
                    if (!MoonBoxModule.clickArticleEntry(retryCount)) {
                        retryCount++;
                        if (retryCount >= maxRetries) {
                            console.log("❌ 重试3次后仍无法找到优选文章入口，结束并提醒用户");
                            ui.run(() => toast("微信限制，等待脚本更新"));
                            return false;
                        }
                        continue;
                    }

                    // 步骤6：查找关注公众号按钮并点击,如果返回了false，说明已经关注了，直接跳过执行下一步
                    MoonBoxModule.clickFollowButton(retryCount);


                    // 步骤7：获取公众号名称并检查是否在失败记录中
                    currentAccountName = MoonBoxModule.getAccountNameAndCheck(failedAccounts);
                    if (!currentAccountName) {
                        // 如果公众号在失败记录中，返回并从第三步开始执行
                        console.log("公众号在失败记录中，重新开始操作");
                        MoonBoxModule.returnToInitialPage(1);
                        continue; // 重新开始整个流程
                    }

                    // 步骤8：点击公众号名称
                    if (!MoonBoxModule.clickAccountName()) {
                        retryCount++;
                        continue;
                    }

                    // 步骤9：使用OCR识别发消息的按钮并点击
                    if (!MoonBoxModule.clickSendMessageButton()) {
                        retryCount++;
                        continue;
                    }

                    // 步骤10：进入到对话框后使用OCR识别聊天气泡中的内容
                    let chatContent = MoonBoxModule.getChatContent();
                    if (!chatContent) {
                        retryCount++;
                        continue;
                    }

                    // 步骤11：记录识别到的消息并执行三次返回到初始页面
                    console.log("✓ 步骤11：记录消息内容: " + chatContent);
                    MoonBoxModule.returnToInitialPageThreeTimes();

                    // 步骤12：查找输入框，如果没有查找到则重新点击粉Ta按钮
                    let inputSuccess = MoonBoxModule.handleInputBoxAndVerifyAccount(selectedFentaButton, currentAccountName);
                    if (!inputSuccess) {
                        retryCount++;
                        continue;
                    }

                    // 步骤13：输入消息
                    if (!MoonBoxModule.inputMessage(chatContent)) {
                        retryCount++;
                        continue;
                    }

                    // 步骤14：查找点击"确认官住"并检查问题提示
                    let confirmResult = MoonBoxModule.clickConfirmAndCheckProblem(currentAccountName, failedAccounts);
                    if (confirmResult === "problem") {
                        // 发现问题，重新开始操作
                        console.log("发现公众号设置问题，重新开始操作");
                        continue;
                    } else if (confirmResult === "success") {
                        // 操作成功
                        console.log("✓ 单次优号操作成功完成");
                        return true;
                    } else {
                        // 其他错误，重试
                        retryCount++;
                        continue;
                    }

                } catch (error) {
                    console.error("单次优号操作异常: " + error.message);
                    retryCount++;

                    // 尝试返回到初始状态
                    try {
                        MoonBoxModule.returnToInitialPage(3);
                    } catch (e) {
                        console.log("返回初始状态异常: " + e.message);
                    }
                }
            }

            console.log("❌ 单次优号操作失败，已达到最大重试次数");
            return false;
        },

        /**
         * 步骤3：点击"优号按钮"
         * @param {number} retryCount - 当前重试次数
         * @returns {boolean} 是否成功
         */
        clickYouhaoButton: function (retryCount) {
            try {
                console.log("✓ 步骤3：查找并点击优号按钮 (重试次数: " + retryCount + ")");
                let youhaoBtn = className("android.widget.TextView").text("优号").findOne(5000);
                if (!youhaoBtn) {
                    console.log("❌ 未找到优号按钮");
                    return false;
                }
                youhaoBtn.click();
                console.log("✓ 成功点击优号按钮");
                sleep(2000);
                return true;
            } catch (e) {
                console.log("点击优号按钮异常: " + e.message);
                return false;
            }
        },

        /**
         * 步骤4：获取所有粉Ta按钮元素并随机选择一个进行点击
         * @param {number} retryCount - 当前重试次数
         * @returns {Object|null} 选中的粉Ta按钮元素，失败返回null
         */
        getAndClickRandomFentaButton: function (retryCount) {
            try {
                console.log("✓ 步骤4：查找所有粉Ta按钮并随机选择 (重试次数: " + retryCount + ")");

                let fentaButtons = [];

                // 方式3：模糊匹配（按照要求使用方式3）
                try {
                    let buttons3 = className("android.view.View")
                        .textContains("粉")
                        .textContains("Ta")
                        .packageName("com.tencent.mm")
                        .find();
                    if (buttons3.length > 0) {
                        fentaButtons = fentaButtons.concat(buttons3);
                        console.log("✓ 方式3找到 " + buttons3.length + " 个粉Ta按钮");
                    }
                } catch (e) {
                    console.log("方式3查找异常: " + e.message);
                }

                if (fentaButtons.length === 0) {
                    console.log("❌ 未找到任何粉Ta按钮");
                    return null;
                }

                // 随机选择一个按钮
                let randomIndex = Math.floor(Math.random() * fentaButtons.length);
                let selectedButton = fentaButtons[randomIndex];

                console.log("✓ 随机选择第 " + (randomIndex + 1) + " 个粉Ta按钮，位置: " + selectedButton.bounds());
                selectedButton.click();
                console.log("✓ 成功点击粉Ta按钮");
                sleep(2000);
                console.log("返回的元素class:---- " + selectedButton.className());
                return selectedButton;
            } catch (e) {
                console.log("获取并点击粉Ta按钮异常: " + e.message);
                return null;
            }
        },

        /**
         * 步骤5：查找优选文章入口并点击进入优选文章页
         * @param {number} retryCount - 当前重试次数
         * @returns {boolean} 是否成功
         */
        clickArticleEntry: function (retryCount) {
            try {
                console.log("✓ 步骤5：查找优选文章入口 (重试次数: " + retryCount + ")");
                let articleBtn = className("android.widget.TextView").text("点此进入该优号文章").findOne(5000);
                if (!articleBtn) {
                    console.log("❌ 未找到优选文章入口");
                    return false;
                }
                articleBtn.click();
                console.log("✓ 成功点击进入优选文章");
                sleep(3000);

                // 判断是否内容已被发布者删除
                try {
                    let deletedContent = className("android.view.View").text("该内容已被发布者删除").findOne(3000);
                    if (deletedContent) {
                        console.log("该内容已被发布者删除，返回重试");
                        back();
                        sleep(2000);
                        let cancelBtn = className("android.view.View").text("取消").findOne(3000);
                        if (cancelBtn) {
                            cancelBtn.click();
                        }
                        return false;
                    }
                } catch (e) {
                    console.log("检查删除内容异常: " + e.message);
                }

                return true;
            } catch (e) {
                console.log("点击优选文章入口异常: " + e.message);
                return false;
            }
        },

        /**
         * 步骤6：查找关注公众号按钮并点击
         * @param {number} retryCount - 当前重试次数
         * @returns {boolean} 是否成功
         */
        clickFollowButton: function (retryCount) {
            try {
                console.log("✓ 步骤6：查找关注公众号按钮 (重试次数: " + retryCount + ")");

                // 使用方式4多重属性组合进行查找
                let followBtn = className("android.widget.Button")
                    .text("关注")
                    .packageName("com.tencent.mm")
                    .findOne(3000);

                if (!followBtn) {
                    console.log("❌ 未找到关注按钮");
                    return false;
                }

                followBtn.click();
                console.log("✓ 成功点击关注按钮");
                sleep(3000);
                return true;
            } catch (e) {
                console.log("点击关注按钮异常: " + e.message);
                return false;
            }
        },

        /**
         * 步骤7：获取公众号名称并检查是否在失败记录中
         * @param {Array} failedAccounts - 失败公众号记录数组
         * @returns {string|null} 公众号名称，如果在失败记录中则返回null
         */
        getAccountNameAndCheck: function (failedAccounts) {
            try {
                console.log("✓ 步骤7：获取公众号名称并检查失败记录");

                let publicAccountBtn = null;
                let accountName = "";

                // 方式4：通过多重属性组合查找公众号名称
                try {
                    publicAccountBtn = className("android.view.View")
                        .id("js_name")
                        .depth(24)
                        .packageName("com.tencent.mm")
                        .clickable(true)
                        .findOne(3000);
                    if (publicAccountBtn) {
                        console.log("✓ 方式4通过多重属性组合找到公众号名称");
                        accountName = publicAccountBtn.text() || publicAccountBtn.desc() || "";
                    }
                } catch (e) {
                    console.log("方式4查找异常: " + e.message);
                }

                // 如果方式4失败，尝试其他方式
                if (!publicAccountBtn) {
                    try {
                        // 查找关注按钮左侧的兄弟元素
                        let followBtn = className("android.widget.Button").text("已关注").findOne(3000);
                        if (followBtn) {
                            let parent = followBtn.parent();
                            if (parent) {
                                let siblings = parent.children();
                                let followBtnIndex = -1;
                                for (let i = 0; i < siblings.length; i++) {
                                    if (siblings[i].equals(followBtn)) {
                                        followBtnIndex = i;
                                        break;
                                    }
                                }
                                if (followBtnIndex > 0) {
                                    let leftSibling = siblings[followBtnIndex - 1];
                                    accountName = leftSibling.text() || leftSibling.desc() || "";
                                    publicAccountBtn = leftSibling;
                                }
                            }
                        }

                        // 如果没有找到关注按钮左侧的兄弟元素，尝试另外一种方案
                        if (!publicAccountBtn) {
                            console.log("未找到关注按钮左侧兄弟元素，尝试通过赞按钮查找");

                            // 寻找包含"赞"的按钮，赞后面的数字是可变的
                            let likeButtons = className("android.widget.Button").find();
                            let likeBtn = null;

                            for (let i = 0; i < likeButtons.length; i++) {
                                let btnText = likeButtons[i].text();
                                if (btnText && btnText.includes("赞")) {
                                    likeBtn = likeButtons[i];
                                    console.log("✓ 找到赞按钮: " + btnText);
                                    break;
                                }
                            }

                            if (likeBtn) {
                                // 通过赞按钮找到左边的兄弟元素
                                let likeParent = likeBtn.parent();
                                if (likeParent) {
                                    let likeSiblings = likeParent.children();
                                    let likeBtnIndex = -1;

                                    // 找到赞按钮在兄弟元素中的索引
                                    for (let i = 0; i < likeSiblings.length; i++) {
                                        if (likeSiblings[i].equals(likeBtn)) {
                                            likeBtnIndex = i;
                                            break;
                                        }
                                    }

                                    // 获取左侧兄弟元素
                                    if (likeBtnIndex > 0) {
                                        let leftSibling = likeSiblings[likeBtnIndex - 1];
                                        console.log("✓ 找到赞按钮左侧兄弟元素");

                                        // 兄弟元素下面有子元素，循环查找className("android.widget.TextView")的text()
                                        let children = leftSibling.children();
                                        for (let j = 0; j < children.length; j++) {
                                            let child = children[j];
                                            if (child.className() === "android.widget.TextView") {
                                                let childText = child.text();
                                                if (childText && childText.trim().length > 0) {
                                                    accountName = childText.trim();
                                                    publicAccountBtn = child;
                                                    console.log("✓ 通过赞按钮方案找到公众号名称: " + accountName);
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                    } catch (e) {
                        console.log("查找公众号名称异常: " + e.message);
                    }
                }

                if (!accountName) {
                    console.log("❌ 无法获取公众号名称");
                    return null;
                }

                console.log("✓ 获取到公众号名称: " + accountName);

                // 检查该公众号是否在失败记录中
                if (failedAccounts.indexOf(accountName) !== -1) {
                    console.log("⚠️ 该公众号 '" + accountName + "' 在失败记录中，跳过");
                    ui.run(function () {
                        toast("该公众号设置有问题，跳过");
                    });
                    return null;
                }

                // 将公众号按钮保存到模块变量中，供后续步骤使用
                MoonBoxModule.currentPublicAccountBtn = publicAccountBtn;
                return accountName;
            } catch (e) {
                console.log("获取公众号名称异常: " + e.message);
                return null;
            }
        },

        /**
         * 步骤8：点击公众号名称
         * @returns {boolean} 是否成功
         */
        clickAccountName: function () {
            try {
                console.log("✓ 步骤8：点击公众号名称");
                if (MoonBoxModule.currentPublicAccountBtn) {
                    MoonBoxModule.currentPublicAccountBtn.click();
                    console.log("✓ 成功点击公众号名称");
                    sleep(3000);
                    return true;
                } else {
                    console.log("❌ 公众号按钮不存在");
                    return false;
                }
            } catch (e) {
                console.log("点击公众号名称异常: " + e.message);
                return false;
            }
        },

        /**
         * 步骤9：使用OCR识别发消息的按钮并点击
         * @returns {boolean} 是否成功
         */
        clickSendMessageButton: function () {
            try {
                console.log("✓ 步骤9：使用OCR识别发消息按钮");

                // 检查状态
                if (!moonBoxIsRunning) {
                    console.log("❌ 功能已停止");
                    return false;
                }

                // 创建状态检查器
                let statusChecker = {
                    isRunning: function () { return moonBoxIsRunning; },
                    isPaused: function () { return moonBoxIsPaused; }
                };

                let ocrSuccess = findAndClickSendMessageButtonByOCR(statusChecker);
                if (!ocrSuccess) {
                    console.log("❌ OCR识别发消息按钮失败");
                    return false;
                }

                console.log("✓ OCR识别发消息按钮成功");
                sleep(3000);
                return true;
            } catch (e) {
                console.log("OCR识别发消息按钮异常: " + e.message);
                return false;
            }
        },

        /**
         * 步骤10：进入到对话框后使用OCR识别聊天气泡中的内容
         * @returns {string|null} 聊天内容，失败返回null
         */
        getChatContent: function () {
            try {
                console.log("✓ 步骤10：使用OCR识别聊天气泡内容");
                let chatContent = runLink();
                if (chatContent && chatContent.length > 0) {
                    console.log("✓ 成功获取聊天内容: " + chatContent[0]);
                    return chatContent[0];
                } else {
                    console.log("❌ 未获取到聊天内容");
                    return null;
                }
            } catch (e) {
                console.log("获取聊天内容异常: " + e.message);
                return null;
            }
        },

        /**
         * 步骤11：执行三次返回到初始页面
         */
        returnToInitialPageThreeTimes: function () {
            try {
                console.log("✓ 步骤11：执行三次返回到初始页面");
                for (let backCount = 1; backCount <= 3; backCount++) {
                    console.log("执行第" + backCount + "次返回");
                    back();
                    sleep(1000);
                }
                console.log("✓ 成功返回到初始页面");
            } catch (e) {
                console.log("返回初始页面异常: " + e.message);
            }
        },

        /**
         * 步骤12：查找输入框，如果没有查找到则重新点击粉Ta按钮并验证公众号
         * @param {Object} selectedFentaButton - 之前选中的粉Ta按钮
         * @param {string} currentAccountName - 当前公众号名称
         * @returns {boolean} 是否成功
         */
        handleInputBoxAndVerifyAccount: function (selectedFentaButton, currentAccountName) {
            try {
                console.log("✓ 步骤12：查找输入框");
                let inputBox = className("android.widget.EditText").findOne(3000);

                if (!inputBox) {
                    console.log("❌ 未找到输入框，重新点击粉Ta按钮");

                    // 重新点击粉Ta按钮（不使用数组下标方式）
                    if (selectedFentaButton) {
                        console.log("没有找到对话框传入的按钮元素className", selectedFentaButton.className())

                        selectedFentaButton.click();
                        sleep(2000);

                        // 重新进入优选文章页并获取公众号名称
                        if (MoonBoxModule.clickArticleEntry(0)) {
                            let verifyAccountName = MoonBoxModule.getAccountNameForVerification();
                            if (verifyAccountName === currentAccountName) {
                                console.log("✓ 公众号名称验证一致: " + verifyAccountName);
                                // 返回到输入页面
                                back();
                                sleep(1000);
                                back();
                                sleep(1000);
                                back();
                                sleep(1000);

                                // 重新查找输入框
                                inputBox = className("android.widget.EditText").findOne(5000);
                                if (!inputBox) {
                                    console.log("❌ 重试后仍未找到输入框");
                                    return false;
                                }
                            } else {
                                console.log("❌ 公众号名称验证不一致，期望: " + currentAccountName + "，实际: " + verifyAccountName);
                                return false;
                            }
                        } else {
                            console.log("❌ 重新进入优选文章页失败");
                            return false;
                        }
                    } else {
                        console.log("❌ 没有可用的粉Ta按钮");
                        return false;
                    }
                }

                console.log("✓ 成功找到输入框");
                MoonBoxModule.currentInputBox = inputBox;
                return true;
            } catch (e) {
                console.log("处理输入框异常: " + e.message);
                return false;
            }
        },

        /**
         * 获取公众号名称用于验证（步骤12中使用）
         * @returns {string} 公众号名称
         */
        getAccountNameForVerification: function () {
            try {
                let publicAccountBtn = className("android.view.View")
                    .id("js_name")
                    .depth(24)
                    .packageName("com.tencent.mm")
                    .clickable(true)
                    .findOne(3000);

                if (publicAccountBtn) {
                    return publicAccountBtn.text() || publicAccountBtn.desc() || "";
                } else {
                    return "";
                }
            } catch (e) {
                console.log("获取验证公众号名称异常: " + e.message);
                return "";
            }
        },

        /**
         * 步骤13：输入消息
         * @param {string} message - 要输入的消息
         * @returns {boolean} 是否成功
         */
        inputMessage: function (message) {
            try {
                console.log("✓ 步骤13：输入消息");
                if (MoonBoxModule.currentInputBox && message) {
                    MoonBoxModule.currentInputBox.setText(message);
                    console.log("✓ 成功输入消息: " + message);
                    sleep(1000);
                    return true;
                } else {
                    console.log("❌ 输入框不存在或消息为空");
                    return false;
                }
            } catch (e) {
                console.log("输入消息异常: " + e.message);
                return false;
            }
        },

        /**
         * 步骤14：查找点击"确认官住"并检查问题提示
         * @param {string} currentAccountName - 当前公众号名称
         * @param {Array} failedAccounts - 失败公众号记录数组
         * @returns {string} 结果："success"成功，"problem"有问题，"error"错误
         */
        clickConfirmAndCheckProblem: function (currentAccountName, failedAccounts) {
            try {
                console.log("✓ 步骤14：查找确认官住按钮");
                let confirmBtn = className("android.view.View").text("确认官住").findOne(5000);
                if (!confirmBtn) {
                    console.log("❌ 未找到确认官住按钮");
                    return "error";
                }

                confirmBtn.click();
                console.log("✓ 成功点击确认官住按钮");
                sleep(1000);

                // 检查是否有自动回复设置问题的提示（模糊匹配）
                console.log("正在检查是否有自动回复设置问题提示");
                let problemHint = null;

                try {
                    // 模糊匹配提示文字
                    let hintTexts = [
                        "此号自动回复设置有问题",
                        "导致你无法获得应得的豆子",
                        "请立即取关此公众号",
                        "系统不会惩罚你"
                    ];

                    for (let hintText of hintTexts) {
                        problemHint = textContains(hintText).findOne(2000);
                        if (problemHint) {
                            console.log("✓ 发现自动回复设置问题提示: " + problemHint.text());
                            break;
                        }
                    }

                    // 如果没有找到具体文字，尝试查找包含关键词的元素
                    if (!problemHint) {
                        let allTexts = className("android.widget.TextView").find();
                        for (let textView of allTexts) {
                            let text = textView.text();
                            if (text && (text.includes("自动回复设置有问题") ||
                                text.includes("无法获得应得的豆子") ||
                                text.includes("取关此公众号"))) {
                                problemHint = textView;
                                console.log("✓ 通过关键词匹配发现问题提示: " + text);
                                break;
                            }
                        }
                    }
                } catch (e) {
                    console.log("检查提示异常: " + e.message);
                }

                if (problemHint) {
                    console.log("⚠️ 检测到该公众号自动回复设置有问题");
                    ui.run(function () {
                        toast("该公众号自动回复设置有问题，加入失败列表");
                    });

                    // 将该公众号加入失败记录
                    if (failedAccounts.indexOf(currentAccountName) === -1) {
                        failedAccounts.push(currentAccountName);
                        console.log("✓ 已将公众号 '" + currentAccountName + "' 加入失败记录");
                        console.log("当前失败记录: " + JSON.stringify(failedAccounts));
                    }

                    // 点击取消按钮
                    try {
                        let cancelBtn = className("android.view.View").text("取消").findOne(5000);
                        if (cancelBtn) {
                            cancelBtn.click();
                            console.log("✓ 成功点击取消按钮");
                        }
                    } catch (e) {
                        console.log("点击取消按钮异常: " + e.message);
                    }

                    return "problem";
                } else {
                    console.log("✓ 未发现自动回复设置问题，操作成功");
                    return "success";
                }
            } catch (e) {
                console.log("确认官住操作异常: " + e.message);
                return "error";
            }
        },

        /**
         * 返回到初始页面（通用方法）
         */
        returnToInitialPage: function (number) {
            try {
                console.log("尝试返回到初始页面");
                for (let backCount = 0; backCount < number; backCount++) {
                    try {
                        back();
                        sleep(1000);
                    } catch (e) {
                        break;
                    }
                }
                sleep(2000); // 等待界面稳定
            } catch (e) {
                console.log("返回初始页面异常: " + e.message);
            }
        },

        /**
         * 执行单次阅读操作
         * @param {number} index - 当前执行的次数
         * @returns {boolean} 是否成功
         */
        performReadOperation: function (index) {
            try {
                sleep(1000);
                // 1. 寻找阅读按钮并点击（重试机制）
                let readButton = null;
                for (let retry = 0; retry < 3; retry++) {
                    readButton = className("android.widget.TextView").text("阅Ta").findOne(3000);
                    if (readButton) {
                        break;
                    }
                    sleep(1000);
                }

                if (!readButton) {
                    ui.run(function () {
                        console.log("未找到阅读按钮，跳过第" + index + "次操作");
                    });
                    return false;
                }

                // 点击阅读按钮
                try {
                    readButton.parent().click();
                    sleep(1000);
                    try {
                        //如果弹出再读框则找到普通阅读（设置3秒超时）
                        let ptButton = className("android.widget.Button").text("普通阅读").findOne(3000);
                        if (ptButton) {
                            ptButton.click();
                            sleep(1000); // 等待页面跳转
                        }
                    } catch (e) {
                        // 忽略异常
                    }
                } catch (e) {
                    readButton.click();
                }

                // 等待页面加载
                sleep(4000);

                // 检查是否成功进入文章页面
                let pageLoaded = false;
                for (let check = 0; check < 3; check++) {
                    // 检查页面是否包含文章内容的特征元素
                    if (textContains("阅读").exists() || textContains("留言").exists() || textContains("分享").exists()) {
                        pageLoaded = true;
                        break;
                    }
                    sleep(1000);
                }

                if (!pageLoaded) {
                    back();
                    sleep(1000);
                    return false;
                }

                // 2. 在文章页面寻找阅读数量
                let readCount = MoonBoxModule.findAndRecordReadCount();
                if (readCount !== null && readCount !== -1) {
                    console.error("📖 识别到阅读数: " + readCount);

                    // 保存阅读记录
                    MoonBoxModule.saveReadRecord(index, readCount);
                } else {
                    console.error("📖 识别到阅读数: 未识别");
                    // 即使没找到阅读数量，也记录一次操作
                    MoonBoxModule.saveReadRecord(index, -1); // -1表示未找到
                    readCount = -1; // 确保readCount有值
                }

                // 3. 返回上一页
                // 使用配置的滑动次数和间隔进行向下滑动
                let swipeCount = moonBoxConfig.swipeCount || 5; // 默认5次
                let swipeInterval = (moonBoxConfig.swipeInterval || 1) * 1000; // 转换为毫秒，默认1秒

                for (let i = 1; i <= swipeCount; i++) {
                    swipe(device.width / 2, device.height * 0.7, device.width / 2, device.height * 0.3, 500);
                    if (i < swipeCount) { // 最后一次滑动后不需要等待
                        sleep(swipeInterval);
                    }
                }

                back();

                // 等待页面加载
                sleep(2000);

                //输入阅读量
                try {
                    let inputField = className("android.widget.EditText").text("请输阅读数").findOne(3000);
                    if (inputField) {
                        if (readCount !== null && readCount !== -1) {
                            inputField.setText(readCount.toString());
                        } else {
                            inputField.setText("0");
                        }
                    }
                } catch (e) {
                    // 忽略异常
                }

                //点击确定
                try {
                    let confirmBtn = className("android.widget.Button").text("确定").findOne(3000);
                    if (confirmBtn) {
                        confirmBtn.click();
                        console.log("✓ 已点击确定按钮");
                    } else {
                        console.error("✗ 未找到确定按钮");
                    }
                } catch (e) {
                    console.error("✗ 点击确定按钮时发生异常: " + e.message);
                }
                return true;
            } catch (e) {
                console.error("✗ 执行阅读操作时发生异常: " + e.message);
                console.error("异常堆栈: " + e.stack);
                // 尝试返回到安全状态
                try {
                    console.log("尝试恢复到安全状态...");
                    back();
                    sleep(1000);
                    console.log("✓ 已尝试返回到安全状态");
                } catch (backError) {
                    console.error("✗ 返回操作也失败: " + backError.message);
                }
                return false;
            }
        },

        /**
         * 寻找并记录阅读数量
         * @returns {number|null} 阅读数量，如果未找到返回null
         */
        findAndRecordReadCount: function () {
            try {
                console.log("--- 开始智能识别阅读数量 ---");

                // 向下滑动寻找阅读数量，最多滑动8次
                for (let scrollCount = 0; scrollCount < 8; scrollCount++) {
                    console.log("🔍 第 " + (scrollCount + 1) + " 轮搜索阅读数量...");

                    // 方法1: 直接寻找包含"阅读"和数字的文本
                    // console.log("方法1: 搜索包含'阅读'的文本元素...");
                    let readElements = textContains("阅读").find();
                    // console.log("找到 " + readElements.length + " 个包含'阅读'的元素");

                    for (let i = 0; i < readElements.length; i++) {
                        let element = readElements[i];
                        let text = element.text();
                        // console.log("检查文本: '" + text + "'");

                        // 匹配"阅读 数字"或"阅读数字"的模式
                        let match = text.match(/阅读\s*(\d+)/);
                        if (match) {
                            let readCount = parseInt(match[1]);
                            // console.log("🎯 方法1成功！识别到阅读数量: " + readCount);
                            // console.log("✓ 匹配的完整文本: '" + text + "'");
                            return readCount;
                        }
                    }
                    // console.log("方法1未找到匹配项");

                    // 方法2: 寻找"阅读"文字，然后在其附近寻找数字
                    // console.log("方法2: 搜索'阅读'文字并在附近寻找数字...");
                    let readElement = textContains("阅读").findOne(1000);
                    if (readElement) {
                        let readBounds = readElement.bounds();
                        // console.log("找到'阅读'文字，位置: " + JSON.stringify(readBounds));

                        // 在"阅读"文字附近寻找数字
                        let allElements = className("android.widget.TextView").find();
                        // console.log("搜索 " + allElements.length + " 个TextView元素寻找数字...");

                        let candidateNumbers = [];
                        for (let j = 0; j < allElements.length; j++) {
                            let numElement = allElements[j];
                            let numText = numElement.text();

                            // 检查是否为纯数字
                            if (/^\d+$/.test(numText)) {
                                let numBounds = numElement.bounds();

                                // 检查数字是否在"阅读"文字的右侧或下方（在合理范围内）
                                let horizontalDistance = numBounds.left - readBounds.right;
                                let verticalDistance = Math.abs(numBounds.top - readBounds.top);

                                candidateNumbers.push({
                                    text: numText,
                                    bounds: numBounds,
                                    horizontalDistance: horizontalDistance,
                                    verticalDistance: verticalDistance
                                });

                                // 数字应该在"阅读"文字右侧100像素内，垂直距离50像素内
                                if (horizontalDistance >= -10 && horizontalDistance <= 100 && verticalDistance <= 50) {
                                    let readCount = parseInt(numText);
                                    // console.log("🎯 方法2成功！识别到阅读数量: " + readCount);
                                    // console.log("✓ 数字位置: " + JSON.stringify(numBounds));
                                    // console.log("✓ 相对'阅读'文字的距离 - 水平: " + horizontalDistance + "px, 垂直: " + verticalDistance + "px");
                                    return readCount;
                                }
                            }
                        }

                        if (candidateNumbers.length > 0) {
                            // console.log("找到 " + candidateNumbers.length + " 个候选数字，但位置不符合条件:");
                            candidateNumbers.forEach(function (candidate, index) {
                                // console.log("  候选" + (index + 1) + ": " + candidate.text +
                                    // " (水平距离: " + candidate.horizontalDistance +
                                    // "px, 垂直距离: " + candidate.verticalDistance + "px)");
                            });
                        }
                    } else {
                        console.log("未找到'阅读'文字");
                    }

                    // 方法3: 使用UiSelector寻找特定模式
                    // console.log("方法3: 使用正则表达式直接匹配'阅读+数字'模式...");
                    try {
                        let selector = textMatches(/阅读\s*\d+/);
                        let element = selector.findOne(1000);
                        if (element) {
                            let text = element.text();
                            // console.log("找到匹配的文本: '" + text + "'");
                            let match = text.match(/阅读\s*(\d+)/);
                            if (match) {
                                let readCount = parseInt(match[1]);
                                // console.log("🎯 方法3成功！识别到阅读数量: " + readCount);
                                return readCount;
                            }
                        } else {
                            // console.log("方法3未找到匹配的元素");
                        }
                    } catch (e) {
                        // console.log("方法3执行失败: " + e.message);
                    }

                    // 如果没找到，向下滑动继续寻找
                    if (scrollCount < 7) { // 最后一次不滑动
                        console.log("📱 向下滑动寻找更多内容...");
                        swipe(device.width / 2, device.height * 0.8, device.width / 2, device.height * 0.2, 800);
                        sleep(1500);
                        console.log("滑动完成，继续搜索...\n");
                    }
                }

                console.log("❌ 经过8轮搜索仍未找到阅读数量");
                console.log("--- 阅读数量识别结束 ---");
                return null;
            } catch (e) {
                console.error("❌ 寻找阅读数量时发生异常: " + e.message);
                console.error("异常堆栈: " + e.stack);
                return null;
            }
        },

        /**
         * 保存阅读记录
         * @param {number} index - 阅读次数
         * @param {number} readCount - 阅读数量，-1表示未找到
         */
        saveReadRecord: function (index, readCount) {
            try {
                console.log("📝 开始保存阅读记录...");

                // 获取当前时间
                let now = new Date();
                let timeString = now.toLocaleString();

                // 构建记录对象
                let record = {
                    index: index,
                    readCount: readCount,
                    timestamp: timeString,
                    date: now.toDateString(),
                    status: readCount === -1 ? "未找到阅读数量" : "成功"
                };

                console.log("记录详情: " + JSON.stringify(record, null, 2));

                // 获取已有的阅读记录
                console.log("获取历史记录...");
                let existingRecords = StorageModule.get("readRecords", "[]");
                let records = JSON.parse(existingRecords);
                console.log("当前已有 " + records.length + " 条历史记录");

                // 添加新记录
                records.push(record);
                console.log("添加新记录后总计 " + records.length + " 条记录");

                // 保存记录
                console.log("保存记录到存储...");
                let saveResult = StorageModule.set("readRecords", JSON.stringify(records));
                if (saveResult) {
                    console.log("✓ 记录保存成功");
                } else {
                    console.log("✗ 记录保存失败");
                }

                // 显示提示
                ui.run(function () {
                    if (readCount === -1) {
                        console.log("🔔 显示提示: 第" + index + "次阅读完成，但未找到阅读数量");
                        toast("第" + index + "次阅读完成，但未找到阅读数量");
                    } else {
                        console.log("🔔 显示提示: 第" + index + "次阅读完成，阅读量: " + readCount);
                        toast("第" + index + "次阅读完成，阅读量: " + readCount);
                    }
                });

                console.log("📝 阅读记录保存完成\n");
            } catch (e) {
                console.error("❌ 保存阅读记录时发生异常: " + e.message);
                console.error("异常堆栈: " + e.stack);
            }
        },

        /**
         * 执行视频号功能
         */
        executeShipinhao: function () {
            try {
                console.log("执行视频号功能，数量: " + moonBoxConfig.quantity);
                ui.run(function () {
                    toast("正在执行视频号功能...");
                });

                // 这里添加具体的视频号逻辑
                for (let i = 0; i < moonBoxConfig.quantity && moonBoxIsRunning; i++) {
                    // 检查是否被暂停
                    while (moonBoxIsPaused && moonBoxIsRunning) {
                        sleep(500);
                    }

                    // 检查是否被停止
                    if (!moonBoxIsRunning) {
                        break;
                    }

                    console.log("执行第 " + (i + 1) + " 次视频号操作");
                    ui.run(function () {
                        toast("执行第 " + (i + 1) + " 次视频号操作");
                    });
                    // 添加具体的视频号操作

                }

                if (moonBoxIsRunning) {
                    ui.run(function () {
                        toast("视频号功能执行完成！");
                    });
                    console.log("=== 视频号功能执行完成 ===");
                }
                
                // 视频号功能执行完毕后关闭控制台
                setTimeout(function() {
                    console.hide();
                }, 2000); // 延迟2秒后关闭控制台，让用户看到最终结果
                
            } catch (e) {
                console.error("执行视频号功能失败: " + e.message);
                
                // 即使发生异常也关闭控制台
                setTimeout(function() {
                    console.hide();
                }, 2000);
            }
        },

        /**
         * 获取阅读记录
         * @returns {Array} 阅读记录数组
         */
        getReadRecords: function () {
            try {
                let existingRecords = StorageModule.get("readRecords", "[]");
                return JSON.parse(existingRecords);
            } catch (e) {
                console.error("获取阅读记录失败: " + e.message);
                return [];
            }
        },

        /**
         * 清除阅读记录
         */
        clearReadRecords: function () {
            try {
                console.log("🗑️ 开始清除阅读记录...");

                // 先获取当前记录数量
                let existingRecords = StorageModule.get("readRecords", "[]");
                let records = JSON.parse(existingRecords);
                let recordCount = records.length;

                console.log("当前有 " + recordCount + " 条记录需要清除");

                // 清除记录
                let clearResult = StorageModule.set("readRecords", "[]");

                if (clearResult) {
                    console.log("✅ 成功清除 " + recordCount + " 条阅读记录");
                    toast("已清除 " + recordCount + " 条阅读记录");
                } else {
                    console.log("❌ 清除记录失败");
                    toast("清除记录失败");
                }
            } catch (e) {
                console.error("❌ 清除阅读记录时发生异常: " + e.message);
                console.error("异常堆栈: " + e.stack);
                toast("清除记录异常: " + e.message);
            }
        },

        /**
         * 显示阅读记录
         */
        showReadRecords: function () {
            try {
                let records = MoonBoxModule.getReadRecords();
                if (records.length === 0) {
                    toast("暂无阅读记录");
                    return;
                }

                let message = "阅读记录 (共" + records.length + "条):\n\n";
                let successCount = 0;
                let totalReadCount = 0;

                for (let i = 0; i < records.length; i++) {
                    let record = records[i];
                    if (record.readCount !== -1) {
                        message += "第" + record.index + "次: " + record.readCount + " 阅读量\n";
                        successCount++;
                        totalReadCount += record.readCount;
                    } else {
                        message += "第" + record.index + "次: " + (record.status || "未找到阅读数量") + "\n";
                    }
                    message += "时间: " + record.timestamp + "\n\n";
                }

                // 添加统计信息
                message += "统计信息:\n";
                message += "成功获取阅读量: " + successCount + "/" + records.length + " 次\n";
                if (successCount > 0) {
                    message += "总阅读量: " + totalReadCount + "\n";
                    message += "平均阅读量: " + Math.round(totalReadCount / successCount);
                }

                // 显示对话框
                dialogs.alert("阅读记录", message);
            } catch (e) {
                console.error("显示阅读记录失败: " + e.message);
            }
        }
    };
})();

// UI管理器模块 - 专门处理UI界面的创建和切换
const UIManager = (function () {
    return {
        /**
         * 安全地创建UI界面
         * @param {string} layoutXml - UI布局XML
         * @param {function} afterCreation - 创建后的回调函数
         */
        safeCreateUI: function (layoutXml, afterCreation) {
            try {
                // 使用ui.run确保在UI线程执行
                ui.run(function () {
                    try {
                        // 设置UI布局
                        ui.layout(layoutXml);
                        console.log("UI布局已设置成功");

                        // 如果有回调函数，延迟执行以确保UI已完全加载
                        if (afterCreation && typeof afterCreation === 'function') {
                            setTimeout(function () {
                                try {
                                    afterCreation();
                                } catch (e) {
                                    console.error("UI创建后回调执行失败: " + e.message);
                                    console.error(e.stack);
                                }
                            }, 500);
                        }
                    } catch (e) {
                        console.error("设置UI布局失败: " + e.message);
                        console.error(e.stack);
                        toast("创建界面失败: " + e.message);
                    }
                });
            } catch (e) {
                console.error("UI创建失败: " + e.message);
                console.error(e.stack);
                toast("界面创建过程出错: " + e.message);
            }
        },

        /**
         * 创建每日任务界面
         */
        createDailyTaskUI: function () {
            console.log("UIManager: 开始创建每日任务界面");

            // 创建每日任务UI布局
            var dailyTaskLayoutXml =
                '<frame bg="#f5ffe0">' +
                '<vertical padding="8" h="*">' +
                '<horizontal gravity="center_vertical" h="50">' +
                '<button id="backFromDailyTaskBtn" text="返回" textSize="14sp" style="Widget.AppCompat.Button.Borderless" w="60dp" h="40dp"/>' +
                '<text text="每日任务" textSize="18sp" textColor="#333333" gravity="center" layout_weight="1" textStyle="bold"/>' +
                '<frame w="60dp" visibility="invisible"/>' + // 添加一个不可见的占位符，平衡左侧返回按钮
                '</horizontal>' +

                '<scroll layout_weight="1">' +
                '<vertical>' +
                '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                '<vertical padding="0">' +
                '<img id="promotionImage" src="@android:drawable/ic_menu_gallery" w="*" h="200" scaleType="centerCrop"/>' +
                '</vertical>' +
                '</card>' +

                '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                '<vertical padding="15">' +
                '<text id="promotionTitle" text="每日任务" textSize="16sp" textColor="#333333" textStyle="bold"/>' +
                '<text id="promotionContent" text="加载中..." textSize="14sp" textColor="#666666" marginTop="8"/>' +
                '</vertical>' +
                '</card>' +
                '</vertical>' +
                '</scroll>' +

                '<vertical padding="5 10" bg="#f5ffe0">' +
                '<horizontal margin="0 5" gravity="center">' +
                '<button id="saveImageBtn" text="保存图片" textSize="14sp" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                '<button id="copyPromotionBtn" text="复制推广信息" textSize="14sp" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                '</horizontal>' +

                '<horizontal margin="0 0 0 5" gravity="center">' +
                '<button id="shareToFriendsBtn" text="发送到朋友圈" textSize="14sp" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                '<button id="getRewardBtn" text="领取积分奖励" textSize="14sp" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                '</horizontal>' +
                '</vertical>' +
                '</vertical>' +
                '</frame>';

            // 使用安全的UI创建方法
            this.safeCreateUI(dailyTaskLayoutXml, function () {
                try {
                    console.log("每日任务界面创建完成，开始绑定事件");

                    // 设置当前页面状态
                    UIModule.setCurrentPage("dailyTask");

                    // 返回按钮点击事件
                    if (ui.backFromDailyTaskBtn) {
                        ui.backFromDailyTaskBtn.on("click", function () {
                            console.log("每日任务页面返回按钮被点击");

                            // 立即创建脚本中心UI，不使用ui.run包装，避免延迟
                            try {
                                // 直接返回脚本中心并切换到"我的"标签
                                UIModule.returnToScriptCenterMyTab();
                            } catch (e) {
                                console.error("返回脚本中心界面失败: " + e.message);
                                toast("返回失败，请重试");
                            }
                        });
                        console.log("每日任务页面返回按钮事件已绑定");
                    }

                    // 获取推广内容
                    console.log("开始获取推广内容");
                    NetworkModule.getRandomPromotion(function (error, result) {
                        if (error) {
                            console.error("获取推广内容失败: " + error.message);
                            toast("获取推广内容失败，请检查网络连接");
                            return;
                        }

                        if (result && result.code === 200 && result.data) {
                            console.log("成功获取推广内容");
                            var promotionData = result.data;

                            // 更新UI内容
                            ui.run(function () {
                                try {
                                    // 设置标题和内容
                                    if (ui.promotionTitle && promotionData.title) {
                                        ui.promotionTitle.setText(promotionData.title);
                                    }

                                    if (ui.promotionContent && promotionData.content) {
                                        ui.promotionContent.setText(promotionData.content);
                                    }

                                    // 设置图片
                                    if (ui.promotionImage && promotionData.imageUrl) {
                                        try {
                                            // 尝试加载图片
                                            let imageUrl = promotionData.imageUrl;
                                            console.log("尝试加载图片: " + imageUrl);

                                            // 在子线程中加载图片，避免NetworkOnMainThreadException
                                            threads.start(function () {
                                                try {
                                                    // 使用原生方法加载图片
                                                    let imgWrapper = images.load(imageUrl);
                                                    if (imgWrapper) {
                                                        // 将ImageWrapper转换为Bitmap
                                                        let bitmap = imgWrapper.getBitmap();

                                                        // 在UI线程中设置图片
                                                        ui.run(function () {
                                                            try {
                                                                ui.promotionImage.setImageBitmap(bitmap);
                                                                console.log("图片加载成功");
                                                            } catch (e) {
                                                                console.error("设置图片到UI失败: " + e.message);

                                                                // 尝试使用另一种方式设置图片
                                                                try {
                                                                    let drawable = new android.graphics.drawable.BitmapDrawable(context.getResources(), bitmap);
                                                                    ui.promotionImage.setImageDrawable(drawable);
                                                                    console.log("使用Drawable方式设置图片成功");
                                                                } catch (e2) {
                                                                    console.error("使用Drawable设置图片也失败: " + e2.message);
                                                                }
                                                            }
                                                        });
                                                    } else {
                                                        console.error("加载图片失败: 无法获取位图");
                                                    }
                                                } catch (threadError) {
                                                    console.error("子线程加载图片失败: " + threadError.message);
                                                }
                                            });
                                        } catch (imgError) {
                                            console.error("加载图片失败: " + imgError.message);
                                        }
                                    }

                                    // 保存推广数据到全局变量，以便复制按钮使用
                                    ConfigModule.set("currentPromotion", JSON.stringify(promotionData));

                                } catch (uiError) {
                                    console.error("更新推广UI失败: " + uiError.message);
                                }
                            });
                        }
                    });

                    // 保存图片按钮
                    if (ui.saveImageBtn) {
                        ui.saveImageBtn.on("click", function () {
                            try {
                                // 尝试获取当前显示的图片
                                let img = ui.promotionImage.getDrawable();
                                if (img) {
                                    // 将Drawable转换为Bitmap
                                    let bitmap = android.graphics.Bitmap.createBitmap(
                                        img.getIntrinsicWidth(),
                                        img.getIntrinsicHeight(),
                                        android.graphics.Bitmap.Config.ARGB_8888
                                    );
                                    let canvas = new android.graphics.Canvas(bitmap);
                                    img.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
                                    img.draw(canvas);

                                    // 保存图片到相册
                                    let fileName = "promotion_" + new Date().getTime() + ".png";
                                    let path = "/sdcard/Pictures/" + fileName;

                                    // 确保目录存在
                                    files.ensureDir("/sdcard/Pictures/");

                                    // 使用正确的方式保存图片
                                    try {
                                        // 方法1：使用java的方式保存
                                        let fileOutputStream = new java.io.FileOutputStream(path);
                                        bitmap.compress(android.graphics.Bitmap.CompressFormat.PNG, 100, fileOutputStream);
                                        fileOutputStream.close();
                                        console.log("使用Java方式保存图片成功");
                                    } catch (e1) {
                                        console.error("Java方式保存失败: " + e1.message);

                                        try {
                                            // 方法2：使用Auto.js的images.save方法（正确参数）
                                            let imgWrapper = images.fromBitmap(bitmap);
                                            images.save(imgWrapper, path);
                                            console.log("使用Auto.js方式保存图片成功");
                                        } catch (e2) {
                                            console.error("Auto.js方式保存失败: " + e2.message);
                                            throw e2;
                                        }
                                    }

                                    // 通知媒体库更新
                                    try {
                                        media.scanFile(path);
                                    } catch (e) {
                                        console.error("媒体库更新失败: " + e.message);
                                    }

                                    toast("图片已保存到相册: " + fileName);
                                    console.log("图片已保存: " + path);
                                } else {
                                    toast("没有可保存的图片");
                                    console.log("没有可保存的图片");
                                }
                            } catch (e) {
                                toast("保存图片失败: " + e.message);
                                console.error("保存图片失败: " + e.message);
                            }
                        });
                    }

                    // 复制推广信息按钮
                    if (ui.copyPromotionBtn) {
                        ui.copyPromotionBtn.on("click", function () {
                            try {
                                // 获取保存的推广数据
                                let promotionJson = ConfigModule.get("currentPromotion");
                                let promotionData = promotionJson ? JSON.parse(promotionJson) : null;

                                // 获取邀请码
                                let invitationCode = ConfigModule.get("invitationCode") || "未设置";

                                // 构建推广内容
                                let content = "";

                                if (promotionData && promotionData.content) {
                                    content = promotionData.content + "\n\n";
                                } else {
                                    content = "这是推广内容示例，实际内容请联系客服获取。\n\n";
                                }

                                // 添加邀请码
                                content += "邀请码: " + invitationCode;

                                // 复制到剪贴板
                                setClip(content);
                                toast("推广内容已复制到剪贴板");
                                console.log("推广内容已复制: " + content);
                            } catch (e) {
                                toast("复制推广内容失败: " + e.message);
                                console.error("复制推广内容失败: " + e.message);
                            }
                        });
                    }

                    // 发送到朋友圈按钮
                    if (ui.shareToFriendsBtn) {
                        ui.shareToFriendsBtn.on("click", function () {
                            try {

                                // 导入必要的类
                                importClass(android.content.Intent);
                                importClass(android.content.ComponentName);

                                // 准备打开微信的意图
                                let intent = new Intent();
                                intent.setAction("android.intent.action.MAIN");
                                intent.addCategory("android.intent.category.LAUNCHER");
                                intent.setPackage("com.tencent.mm"); // 微信的包名

                                // 获取微信启动页
                                let packageManager = context.getPackageManager();
                                let resolveInfo = packageManager.queryIntentActivities(intent, 0);

                                if (resolveInfo && resolveInfo.size() > 0) {
                                    // 存在微信应用，获取启动页面的组件信息
                                    let activityInfo = resolveInfo.get(0).activityInfo;
                                    let componentName = new ComponentName(activityInfo.packageName, activityInfo.name);

                                    // 设置要启动的组件
                                    intent.setComponent(componentName);
                                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

                                    // 启动微信
                                    context.startActivity(intent);
                                    LogModule.log("成功跳转到微信", "INFO");
                                } else {
                                    toast("未安装微信或无法启动微信");
                                    LogModule.log("未安装微信或无法启动微信", "WARN");
                                }
                            } catch (e) {
                                console.error("跳转到微信失败: " + e.message);
                                console.error(e.stack);
                                LogModule.log("跳转到微信失败: " + e.message, "ERROR");
                                toast("跳转到微信失败: " + e.message);
                            }
                        });
                    }

                    // 领取积分奖励按钮
                    if (ui.getRewardBtn) {
                        ui.getRewardBtn.on("click", function () {
                            toast("请联系客服领取积分奖励");
                        });
                    }

                    console.log("每日任务界面所有事件绑定完成");
                } catch (e) {
                    console.error("绑定每日任务界面事件失败: " + e.message);
                    console.error(e.stack);
                }
            });
        },

        /**
         * 创建代理分销界面
         */
        createAgentSalesUI: function () {
            try {
                console.log("开始创建代理分销界面");

                // 创建UI
                var agentSalesLayoutXml =
                    '<frame w="*" h="*" background="#f5f5f5">' +
                    '<vertical w="*" h="*" padding="16">' +
                    '<horizontal w="*" h="50" gravity="center_vertical">' +
                    '<img id="backBtn" src="@android:drawable/ic_menu_revert" w="24" h="24" tint="#333333" margin="0 0 16 0"/>' +
                    '<frame w="0" layout_weight="1" gravity="center">' +
                    '<text text="代理分销" textSize="18sp" textColor="#333333" textStyle="bold"/>' +
                    '</frame>' +
                    '<frame w="24" visibility="invisible"/>' +
                    '</horizontal>' +

                    '<card w="*" h="auto" margin="0 16 0 0" cardCornerRadius="8dp" cardElevation="2dp">' +
                    '<vertical padding="16">' +
                    '<horizontal>' +
                    '<text text="我的佣金：" textSize="16sp" textColor="#333333"/>' +
                    '<text id="commissionText" text="0.00元" textSize="16sp" textColor="#FF5722" textStyle="bold"/>' +
                    '</horizontal>' +
                    '<horizontal margin="0 8 0 0">' +
                    '<text text="推荐总人数：" textSize="16sp" textColor="#333333"/>' +
                    '<text id="totalReferralsText" text="0" textSize="16sp" textColor="#333333" textStyle="bold"/>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</card>' +

                    '<card id="level1Card" w="*" h="auto" margin="0 16 0 0" cardCornerRadius="8dp" cardElevation="2dp">' +
                    '<vertical>' +
                    '<horizontal id="level1Header" padding="16 16" gravity="center_vertical" bg="#f9f9f9">' +
                    '<text id="level1Title" text="一级会员(0)" textSize="16sp" textColor="#333333" textStyle="bold" layout_weight="1"/>' +
                    '<text id="level1Arrow" text="▶" textSize="16sp" textColor="#666666"/>' +
                    '</horizontal>' +
                    '<vertical id="level1Content" padding="8 0" visibility="gone">' +
                    '<horizontal padding="10 8" bg="#eeeeee">' +
                    '<text text="用户名" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '<text text="收益金额" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '<text text="收益时间" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '</horizontal>' +
                    '<!-- 会员列表内容将动态添加 -->' +
                    '</vertical>' +
                    '</vertical>' +
                    '</card>' +

                    '<card id="level2Card" w="*" h="auto" margin="0 16 0 0" cardCornerRadius="8dp" cardElevation="2dp">' +
                    '<vertical>' +
                    '<horizontal id="level2Header" padding="16 16" gravity="center_vertical" bg="#f9f9f9">' +
                    '<text id="level2Title" text="二级会员(0)" textSize="16sp" textColor="#333333" textStyle="bold" layout_weight="1"/>' +
                    '<text id="level2Arrow" text="▶" textSize="16sp" textColor="#666666"/>' +
                    '</horizontal>' +
                    '<vertical id="level2Content" padding="8 0" visibility="gone">' +
                    '<horizontal padding="10 8" bg="#eeeeee">' +
                    '<text text="用户名" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '<text text="收益金额" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '<text text="收益时间" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '</horizontal>' +
                    '<!-- 会员列表内容将动态添加 -->' +
                    '</vertical>' +
                    '</vertical>' +
                    '</card>' +
                    '</vertical>' +
                    '</frame>';

                // 使用安全的UI创建方法
                ui.layout(agentSalesLayoutXml);

                // 设置当前页面状态
                UIModule.setCurrentPage("agentSales");

                // 加载分销数据
                this.loadDistributionData();

                // 绑定事件
                if (ui.backBtn) {
                    ui.backBtn.on("click", function () {
                        UIManager.closeAgentSalesUI();
                    });
                }

                // 绑定一级会员展开/收起事件
                if (ui.level1Header) {
                    ui.level1Header.on("click", function () {
                        UIManager.toggleMemberList("level1");
                    });
                }

                // 绑定二级会员展开/收起事件
                if (ui.level2Header) {
                    ui.level2Header.on("click", function () {
                        UIManager.toggleMemberList("level2");
                    });
                }

                console.log("代理分销界面所有事件绑定完成");
            } catch (e) {
                console.error("创建代理分销界面出错：" + e);
            }
        },

        /**
         * 加载分销数据
         */
        loadDistributionData: function () {
            try {
                console.log("开始加载分销数据");

                // 获取用户token
                let token = ConfigModule.get("userToken");
                if (!token) {
                    console.error("未找到用户token，无法获取分销数据");
                    toast("请先登录");
                    return;
                }

                // 调用API获取分销统计数据
                NetworkModule.get("/user/distribution/statistics", null, function (error, result) {
                    if (error) {
                        console.error("获取分销数据失败: " + error.message);
                        toast("获取分销数据失败，请检查网络连接");
                        return;
                    }

                    if (result && result.code === 200 && result.data) {
                        console.log("成功获取分销数据");
                        var distributionData = result.data;

                        // 更新UI内容
                        ui.run(function () {
                            try {
                                // 设置佣金信息
                                if (ui.commissionText) {
                                    ui.commissionText.setText(distributionData.totalAmount + "元");
                                }

                                // 设置推荐总人数
                                if (ui.totalReferralsText) {
                                    ui.totalReferralsText.setText(distributionData.totalReferrals + "");
                                }

                                // 更新一级会员标题和数据
                                if (ui.level1Title) {
                                    ui.level1Title.setText("一级会员(" + distributionData.level1Count + ")");
                                }

                                // 更新二级会员标题和数据
                                if (ui.level2Title) {
                                    ui.level2Title.setText("二级会员(" + distributionData.level2Count + ")");
                                }

                                // 处理会员数据并填充会员列表
                                if (distributionData.level1Details && distributionData.level1Details.length > 0) {
                                    let processedData = UIManager.processMemberData(distributionData.level1Details);
                                    UIManager.fillMemberList("level1", processedData);
                                }

                                if (distributionData.level2Details && distributionData.level2Details.length > 0) {
                                    let processedData = UIManager.processMemberData(distributionData.level2Details);
                                    UIManager.fillMemberList("level2", processedData);
                                }

                                console.log("分销数据加载完成");
                            } catch (uiError) {
                                console.error("更新分销UI失败: " + uiError.message);
                            }
                        });
                    } else {
                        console.error("获取分销数据失败: " + (result ? result.message : "未知错误"));
                        toast("获取分销数据失败: " + (result ? result.message : "未知错误"));
                    }
                }, token);
            } catch (e) {
                console.error("加载分销数据出错：" + e);
            }
        },

        /**
         * 处理会员数据，归集相同用户的记录
         * @param {Array} data - 原始会员数据数组
         * @returns {Array} 处理后的会员数据数组
         */
        processMemberData: function (data) {
            try {
                console.log("开始处理会员数据，归集相同用户记录");

                if (!data || !Array.isArray(data) || data.length === 0) {
                    return [];
                }

                // 用于存储归集后的数据
                let processedData = {};

                // 遍历原始数据，按用户ID归集
                data.forEach(function (item) {
                    let userId = item.referralUserId;
                    if (!userId) return;

                    // 如果该用户ID还未记录，则创建新记录
                    if (!processedData[userId]) {
                        processedData[userId] = {
                            referralUserId: userId,
                            referralPhoneMasked: item.referralPhoneMasked || "未知用户",
                            amount: 0,
                            createTime: item.createTime || "",
                            hasIncome: false
                        };
                    }

                    // 累加金额
                    if (item.amount) {
                        processedData[userId].amount += parseFloat(item.amount);

                        // 如果有金额，则标记为有收益
                        if (parseFloat(item.amount) > 0) {
                            processedData[userId].hasIncome = true;
                        }

                        // 更新为最新的时间（如果当前记录的时间更新）
                        if (item.createTime && (!processedData[userId].createTime ||
                            new Date(item.createTime) > new Date(processedData[userId].createTime))) {
                            processedData[userId].createTime = item.createTime;
                        }
                    }
                });

                // 转换为数组
                let result = Object.values(processedData);

                // 按金额从大到小排序
                result.sort(function (a, b) {
                    return b.amount - a.amount;
                });

                console.log("会员数据处理完成，归集后共" + result.length + "条记录");
                return result;
            } catch (e) {
                console.error("处理会员数据出错：" + e);
                return data || [];
            }
        },

        /**
         * 填充会员列表数据
         * @param {string} level - 会员级别：level1, level2
         * @param {Array} data - 会员数据数组
         */
        fillMemberList: function (level, data) {
            try {
                console.log("填充" + level + "会员列表数据");
                let container;

                // 确保数据有效
                if (!data || !Array.isArray(data) || data.length === 0) {
                    console.log(level + "会员数据为空或无效");
                    return;
                }

                // 根据级别获取对应容器
                if (level === "level1") {
                    container = ui.level1Content;
                } else if (level === "level2") {
                    container = ui.level2Content;
                }

                // 确保容器存在
                if (!container) {
                    console.error("未找到" + level + "容器UI元素");
                    return;
                }

                // 清除现有视图（除了标题行）
                container.removeAllViews();

                // 添加标题行
                let headerXml =
                    '<horizontal padding="10 8" bg="#eeeeee">' +
                    '<text text="用户名" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '<text text="收益金额" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '<text text="收益时间" w="0" layout_weight="1" textSize="14sp" textColor="#666666" textStyle="bold"/>' +
                    '</horizontal>';

                try {
                    let headerRow = ui.inflate(headerXml, container, true);
                } catch (e) {
                    console.error("添加标题行失败: " + e);
                }

                // 添加会员行
                for (let i = 0; i < data.length; i++) {
                    try {
                        let member = data[i];
                        let rowLayout = new android.widget.LinearLayout(context);
                        rowLayout.setOrientation(android.widget.LinearLayout.HORIZONTAL);
                        rowLayout.setLayoutParams(new android.widget.LinearLayout.LayoutParams(
                            android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                            android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
                        ));
                        rowLayout.setPadding(10, 5, 10, 5);

                        // 设置背景色
                        if (i % 2 === 0) {
                            rowLayout.setBackgroundColor(android.graphics.Color.parseColor("#ffffff"));
                        } else {
                            rowLayout.setBackgroundColor(android.graphics.Color.parseColor("#f8f8f8"));
                        }

                        // 创建用户名文本
                        let phoneText = new android.widget.TextView(context);
                        let phoneParams = new android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1);
                        phoneText.setLayoutParams(phoneParams);
                        phoneText.setText(member.referralPhoneMasked || "未知用户");
                        phoneText.setTextSize(14);
                        phoneText.setTextColor(android.graphics.Color.parseColor("#333333"));
                        rowLayout.addView(phoneText);

                        // 创建金额文本
                        let amountText = new android.widget.TextView(context);
                        let amountParams = new android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1);
                        amountText.setLayoutParams(amountParams);
                        amountText.setText(member.amount.toFixed(2) + "元");
                        amountText.setTextSize(14);
                        amountText.setTextColor(android.graphics.Color.parseColor("#FF5722"));
                        rowLayout.addView(amountText);

                        // 创建时间文本
                        let timeText = new android.widget.TextView(context);
                        let timeParams = new android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1);
                        timeText.setLayoutParams(timeParams);

                        // 如果金额为0，则不显示时间
                        let timeStr = "";
                        if (member.hasIncome && member.amount > 0) {
                            // 将时间格式化为"2023.06.01"的形式
                            let createTime = member.createTime || "";
                            if (createTime && createTime.length >= 10) {
                                timeStr = createTime.substring(0, 10).replace(/-/g, ".");
                            }
                        }

                        timeText.setText(timeStr);
                        timeText.setTextSize(14);
                        timeText.setTextColor(android.graphics.Color.parseColor("#666666"));
                        rowLayout.addView(timeText);

                        // 添加到容器
                        ui.run(function () {
                            container.addView(rowLayout);
                        });
                    } catch (e) {
                        console.error("添加会员行失败: " + e);
                    }
                }

                console.log(level + "会员列表数据填充完成");
            } catch (e) {
                console.error("填充会员列表数据出错：" + e);
            }
        },

        // 关闭代理分销界面
        closeAgentSalesUI: function () {
            try {
                console.log("关闭代理分销界面");
                // 直接返回脚本中心并切换到"我的"标签
                UIModule.returnToScriptCenterMyTab();
            } catch (e) {
                console.error("关闭代理分销界面出错：" + e);
            }
        },

        /**
         * 切换会员列表的展开/收起状态
         * @param {string} level - 会员级别：level1, level2
         */
        toggleMemberList: function (level) {
            try {
                console.log("切换" + level + "会员列表显示状态");
                let content, arrow;

                if (level === "level1") {
                    content = ui.level1Content;
                    arrow = ui.level1Arrow;
                } else if (level === "level2") {
                    content = ui.level2Content;
                    arrow = ui.level2Arrow;
                }

                if (!content || !arrow) {
                    console.error("未找到" + level + "相关UI元素");
                    return;
                }

                // 使用ui.run确保在UI线程中执行
                ui.run(function () {
                    try {
                        // 获取当前可见性
                        let isVisible = content.getVisibility() === android.view.View.VISIBLE;

                        // 切换可见性
                        if (isVisible) {
                            content.setVisibility(android.view.View.GONE);
                            arrow.setText("▶");
                            console.log(level + "会员列表已收起");
                        } else {
                            content.setVisibility(android.view.View.VISIBLE);
                            arrow.setText("▼");
                            console.log(level + "会员列表已展开");
                        }
                    } catch (e) {
                        console.error("切换显示状态失败: " + e);
                    }
                });
            } catch (e) {
                console.error("切换会员列表显示状态出错：" + e);
            }
        },

        /**
         * 创建推广排名界面
         */
        createPromotionRankUI: function () {
            try {
                console.log("UIManager: 开始创建推广排名界面");
                LogModule.log("开始创建推广排名界面", "INFO");
                var that = this;

                // 创建推广排名UI布局
                var promotionRankLayoutXml =
                    '<frame>' +
                    '<vertical padding="10 5" bg="#FAFAFA">' +
                    '<horizontal gravity="center_vertical" padding="10">' +
                    '<img src="@android:drawable/ic_menu_revert" w="24" h="24" tint="#333333" id="promotionRankBackBtn"/>' +
                    '<text text="推广排名" textSize="18sp" textColor="#333333" gravity="center" layout_weight="1" textStyle="bold"/>' +
                    '<frame w="60dp" visibility="invisible"/>' +
                    '</horizontal>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                    '<vertical padding="15">' +
                    '<text text="我的排名" textSize="16sp" textColor="#333333" textStyle="bold"/>' +
                    '<text id="myRankText" text="加载中..." textSize="14sp" textColor="#666666" marginTop="8"/>' +
                    '</vertical>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                    '<vertical padding="15">' +
                    '<text id="activityDescText" text="活动说明" textSize="16sp" textColor="#333333" textStyle="bold"/>' +
                    '<text id="activityDesc" text="加载中..." textSize="14sp" textColor="#666666" marginTop="8"/>' +
                    '</vertical>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                    '<vertical padding="15">' +
                    '<text text="推广排行榜" textSize="16sp" textColor="#333333" textStyle="bold"/>' +
                    '<list id="rankList" h="*">' +
                    '<horizontal padding="10 8" w="*">' +
                    '<text text="{{rank}}" w="40" textSize="14sp" textColor="#333333"/>' +
                    '<text text="{{phoneMasked}}" layout_weight="1" textSize="14sp" textColor="#333333"/>' +
                    '<text text="{{totalReferrals}}人" w="60" textSize="14sp" textColor="#FF5722" gravity="right"/>' +
                    '</horizontal>' +
                    '</list>' +
                    '</vertical>' +
                    '</card>' +
                    '</vertical>' +
                    '</frame>';

                // 使用安全的UI创建方法
                this.safeCreateUI(promotionRankLayoutXml, function () {
                    try {
                        console.log("推广排名界面创建完成，开始绑定事件");

                        // 设置当前页面状态
                        UIModule.setCurrentPage("promotionRank");

                        // 返回按钮点击事件
                        ui.promotionRankBackBtn.on("click", function () {
                            console.log("推广排名页面返回按钮被点击");
                            UIModule.createScriptCenterUI(false);
                            return true; // 阻止事件继续传播
                        });
                        console.log("推广排名页面返回按钮事件已绑定");

                        // 获取并显示排名数据
                        that.fetchAndShowRankData();
                    } catch (e) {
                        console.error("推广排名界面事件绑定失败: " + e.message);
                        LogModule.log("推广排名界面事件绑定失败: " + e.message, "ERROR");
                    }
                });
            } catch (e) {
                console.error("创建推广排名界面失败: " + e.message);
                LogModule.log("创建推广排名界面失败: " + e.message, "ERROR");
            }
        },

        /**
         * 获取并显示排名数据
         */
        fetchAndShowRankData: function () {
            try {
                // 初始化排行榜数据
                ui.rankList.setDataSource([]);

                // 获取推广排名数据
                console.log("开始获取推广排名数据");
                NetworkModule.getPromotionRank(function (error, result) {
                    if (error) {
                        console.error("获取推广排名数据失败: " + error.message);
                        toast("获取推广排名数据失败，请检查网络连接");
                        return;
                    }

                    if (result && result.code === 200 && result.data) {
                        console.log("成功获取推广排名数据");
                        var rankData = result.data;

                        // 更新UI内容
                        ui.run(function () {
                            try {
                                // 设置活动说明
                                if (ui.activityDesc) {
                                    var activityInfo = "";
                                    if (rankData.description) {
                                        activityInfo += rankData.description + "\n";
                                    }
                                    if (rankData.startTime && rankData.endTime) {
                                        activityInfo += "活动时间: " + rankData.startTime + " 至 " + rankData.endTime;
                                    }

                                    if (activityInfo) {
                                        ui.activityDesc.setText(activityInfo);
                                    } else {
                                        ui.activityDesc.setText("暂无活动说明");
                                    }
                                }

                                // 设置我的排名信息
                                if (ui.myRankText) {
                                    var myRankInfo = "";
                                    if (rankData.userRank && rankData.userRank <= 30) {
                                        myRankInfo = "当前排名: 第" + rankData.userRank + "名\n";
                                    } else {
                                        myRankInfo = "当前排名: 未上榜\n";
                                    }

                                    if (rankData.userInfo) {
                                        myRankInfo += "推广总人数: " + rankData.userInfo.totalReferrals + "人\n";
                                        myRankInfo += "一级推广: " + rankData.userInfo.level1Count + "人\n";
                                        myRankInfo += "二级推广: " + rankData.userInfo.level2Count + "人";
                                    }

                                    ui.myRankText.setText(myRankInfo);
                                } else {
                                    ui.myRankText.setText("暂无排名数据");
                                }

                                // 设置排行榜数据
                                if (rankData.rankList && rankData.rankList.length > 0) {
                                    // 转换数据格式
                                    var listData = [];
                                    for (var i = 0; i < rankData.rankList.length; i++) {
                                        var item = rankData.rankList[i];
                                        listData.push({
                                            rank: (i + 1),
                                            phoneMasked: item.phoneMasked || "未知用户",
                                            totalReferrals: item.totalReferrals || 0
                                        });
                                    }

                                    // 更新列表数据
                                    ui.rankList.setDataSource(listData);
                                }
                            } catch (uiError) {
                                console.error("更新推广排名UI失败: " + uiError.message);
                            }
                        });
                    } else {
                        console.error("获取推广排名数据失败: " + (result ? result.message : "未知错误"));
                        toast("获取推广排名数据失败: " + (result ? result.message : "未知错误"));
                    }
                });
            } catch (e) {
                console.error("获取排名数据失败: " + e.message);
                LogModule.log("获取排名数据失败: " + e.message, "ERROR");
            }
        },

        /**
         * 创建关于我们界面（对话框形式）
         * 从后端获取数据并展示
         */
        createAboutUsUI: function () {
            try {
                console.log("UIManager: 开始创建关于我们对话框");
                LogModule.log("开始创建关于我们对话框", "INFO");
                // 尝试从后端获取数据（不阻塞UI显示）
                try {
                    // 获取用户Token
                    let userToken = ConfigModule.get("userToken");
                    if (!userToken) {
                        console.error("获取用户Token失败，可能未登录");
                        return;
                    }

                    // 从后端获取关于我们信息
                    NetworkModule.get("/about-us", null, function (error, result) {
                        if (error) {
                            console.error("获取关于我们信息失败: " + error.message);
                            LogModule.log("获取关于我们信息失败: " + error.message, "ERROR");
                            return;
                        }

                        if (result && result.code === 200 && result.data && result.data.length > 0) {
                            // 获取第一条数据
                            let aboutInfo = result.data[0];

                            // 更新对话框内容
                            ui.run(function () {
                                dialogs.build({
                                    title: aboutInfo.title || "关于我们",
                                    content: aboutInfo.content || "暂无内容",
                                    positive: "确定",
                                    cancelable: true
                                }).show();
                            });

                            console.log("关于我们对话框已更新");
                        }
                    }, userToken); // 传递用户Token用于认证
                } catch (networkError) {
                    console.error("获取关于我们网络请求失败: " + networkError.message);
                    LogModule.log("获取关于我们网络请求失败: " + networkError.message, "ERROR");
                }

            } catch (e) {
                console.error("创建关于我们对话框失败: " + e.message);
                console.error(e.stack);
                LogModule.log("创建关于我们对话框失败: " + e.message, "ERROR");

            }
        },

        createMoreProjectsUI: function () {
            console.log("UIManager: 开始创建更多项目对话框");
            LogModule.log("开始创建更多项目对话框", "INFO");

            try {
                // 请求后端接口获取更多项目链接
                NetworkModule.get("/system/config/key/MORE_PROJECTS_LINK", null, function (error, result) {
                    if (error) {
                        console.error("获取更多项目链接失败: " + error.message);
                        LogModule.log("获取更多项目链接失败: " + error.message, "ERROR");
                        showDefaultMoreProjectsDialog();
                        return;
                    }

                    try {
                        if (result && result.code === 200 && result.data && result.data.configValue) {
                            let projectLink = result.data.configValue;
                            LogModule.log("成功获取更多项目链接: " + projectLink, "INFO");

                            // 使用获取到的链接打开网页
                            ui.run(function () {
                                app.openUrl(projectLink);
                            });
                        } else {
                            throw new Error("获取更多项目链接失败或数据格式错误");
                        }
                    } catch (parseError) {
                        console.error("解析更多项目链接数据失败: " + parseError.message);
                        LogModule.log("解析更多项目链接数据失败: " + parseError.message, "ERROR");
                        showDefaultMoreProjectsDialog();
                    }
                });

                function showDefaultMoreProjectsDialog() {
                    // 网络请求失败时显示默认对话框
                    ui.run(function () {
                        dialogs.build({
                            title: "更多项目",
                            content: "我们团队的其他项目:\n\n1. 短视频助手 - 抖音、快手等平台视频批量下载与管理工具\n\n2. 电商助手 - 淘宝、京东等平台自动化运营与管理工具\n\n3. 游戏辅助 - 多款热门游戏的自动化辅助工具\n\n以上项目正在开发中，敬请期待！",
                            positive: "访问官网",
                            negative: "关闭",
                            cancelable: true
                        })
                            .on("positive", function () {
                                app.openUrl("https://www.example.com");
                            })
                            .show();
                    });
                }

                console.log("更多项目对话框创建完成");
                LogModule.log("更多项目对话框创建完成", "INFO");
            } catch (e) {
                console.error("创建更多项目对话框失败: " + e.message);
                console.error(e.stack);
                LogModule.log("创建更多项目对话框失败: " + e.message, "ERROR");
            }
        },
        //跳转发财交流群
        jumpWealthExchange: function () {
            try {
                // 请求后端接口获取更多项目链接
                NetworkModule.get("/system/config/key/MONEY_GROUP_LINK", null, function (error, result) {
                    if (error) {
                        LogModule.log("获取MONEY_GROUP_LINK链接失败: " + error.message, "ERROR");
                        return;
                    }

                    try {
                        if (result && result.code === 200 && result.data && result.data.configValue) {
                            let projectLink = result.data.configValue;
                            LogModule.log("成功MONEY_GROUP_LINK项目链接: " + projectLink, "INFO");

                            // 使用获取到的链接打开网页
                            ui.run(function () {
                                app.openUrl(projectLink);
                            });
                        } else {
                            throw new Error("获取MONEY_GROUP_LINK失败或数据格式错误");
                        }
                    } catch (parseError) {
                        console.error("解析MONEY_GROUP_LINK链接数据失败: " + parseError.message);
                    }
                });
            } catch (e) {
                console.error("jumpWealthExchange失败: " + e.message);
            }
        },

        //跳转脚本通知群
        jumpScriptNotice: function () {
            try {
                // 请求后端接口获取更多项目链接
                NetworkModule.get("/system/config/key/SCRIPT_NOTICE_GROUP_LINK", null, function (error, result) {
                    if (error) {
                        LogModule.log("获取SCRIPT_NOTICE_GROUP_LINK链接失败: " + error.message, "ERROR");
                        return;
                    }

                    try {
                        if (result && result.code === 200 && result.data && result.data.configValue) {
                            let projectLink = result.data.configValue;
                            LogModule.log("成功SCRIPT_NOTICE_GROUP_LINK项目链接: " + projectLink, "INFO");

                            // 使用获取到的链接打开网页
                            ui.run(function () {
                                app.openUrl(projectLink);
                            });
                        } else {
                            throw new Error("获取SCRIPT_NOTICE_GROUP_LINK失败或数据格式错误");
                        }
                    } catch (parseError) {
                        console.error("解析SCRIPT_NOTICE_GROUP_LINK链接数据失败: " + parseError.message);
                    }
                });
            } catch (e) {
                console.error("jumpScriptNotice失败: " + e.message);
            }
        },

        /**
         * 创建使用教程界面
         */
        createTutorialUI: function () {
            try {
                console.log("UIManager: 开始创建使用教程界面");
                LogModule.log("开始创建使用教程界面", "INFO");
                var that = this;

                // 获取使用教程数据
                console.log("开始获取使用教程数据");
                NetworkModule.get("/system/config/key/TUTORIAL_LINK", null, function (error, result) {
                    if (error) {
                        console.error("获取使用教程数据失败: " + error.message);
                        toast("获取使用教程数据失败，请检查网络连接");
                        return;
                    }

                    if (result && result.code === 200 && result.data && result.data.configValue) {
                        console.log("成功获取使用教程数据");
                        var tutorialLink = result.data.configValue;

                        // 创建包含webview控件的UI布局
                        var tutorialLayoutXml =
                            '<frame>' +
                            '<vertical padding="0" bg="#FAFAFA" h="*" w="*">' +
                            '<horizontal gravity="center_vertical" padding="10">' +
                            '<img src="@android:drawable/ic_menu_revert" w="24" h="24" tint="#333333" id="tutorialBackBtn"/>' +
                            '<text text="使用教程" textSize="18sp" textColor="#333333" gravity="center" layout_weight="1" textStyle="bold"/>' +
                            '<frame w="60dp" visibility="invisible"/>' +
                            '</horizontal>' +

                            // 使用webview控件显示网页内容
                            '<webview id="tutorialWebview" layout_weight="1" h="*" w="*"/>' +
                            '</vertical>' +
                            '</frame>';

                        // 使用安全的UI创建方法
                        that.safeCreateUI(tutorialLayoutXml, function () {
                            try {
                                console.log("使用教程界面创建完成，开始绑定事件");

                                // 设置当前页面状态
                                UIModule.setCurrentPage("tutorial");

                                // 返回按钮点击事件
                                ui.tutorialBackBtn.on("click", function () {
                                    console.log("使用教程页面返回按钮被点击");
                                    UIModule.createScriptCenterUI(false);
                                    return true; // 阻止事件继续传播
                                });
                                console.log("使用教程页面返回按钮事件已绑定");

                                // 加载网页
                                console.log("开始加载教程URL: " + tutorialLink);
                                ui.tutorialWebview.loadUrl(tutorialLink);

                                // 显示加载提示
                                toast("正在加载教程页面...");
                            } catch (e) {
                                console.error("使用教程界面事件绑定失败: " + e.message);
                                console.error(e.stack);
                                LogModule.log("使用教程界面事件绑定失败: " + e.message, "ERROR");

                                // 降级处理：使用外部浏览器打开
                                try {
                                    app.openUrl(tutorialLink);
                                } catch (browserError) {
                                    toast("无法打开链接，请手动访问: " + tutorialLink);
                                }
                            }
                        });
                    } else {
                        console.error("获取使用教程数据失败: " + (result ? result.message : "未知错误"));
                        toast("获取使用教程数据失败: " + (result ? result.message : "未知错误"));
                    }
                });
            } catch (e) {
                console.error("创建使用教程界面失败: " + e.message);
                console.error(e.stack);
                LogModule.log("创建使用教程界面失败: " + e.message, "ERROR");
            }
        },

        /**
         * 获取并显示使用教程数据
         */
        fetchAndShowTutorialData: function () {
            // 此方法不再需要，功能已合并到createTutorialUI中
        }
    };
})();

// 抓包检测模块 - 检测并阻止网络抓包工具
const PacketCaptureDetector = (function () {
    // 缓存检测结果，避免频繁检测
    var lastCheckTime = 0;
    var lastCheckResult = false;
    var checkInterval = 3000; // 3秒内不重复检测
    
    return {
        /**
         * 快速抓包工具检测（用于网络请求前检测）
         * @returns {boolean} 是否检测到抓包工具
         */
        quickCaptureCheck: function () {
            try {
                var currentTime = Date.now();
                
                // 如果距离上次检测时间小于间隔，直接返回上次结果
                if (currentTime - lastCheckTime < checkInterval) {
                    return lastCheckResult;
                }
                
                // 只检测最常见的抓包工具，提高速度
                var commonCaptureTools = [
                    "charles", "com.xk72.charles",
                    "fiddler", "com.telerik.fiddler", 
                    "httpcanary", "com.guoshi.httpcanary",
                    "packetcapture", "app.greyshirts.sslcapture",
                    "reqable", "com.reqable.android", "com.reqable.macos"
                ];
                
                // 快速进程检测 - 减少日志输出，提高性能
                var psResult = shell("ps", true);
                if (psResult && psResult.code === 0 && psResult.result) {
                    var processes = psResult.result.toString().toLowerCase();
                    
                    for (var i = 0; i < commonCaptureTools.length; i++) {
                        var toolName = commonCaptureTools[i].toLowerCase();
                        if (processes.includes(toolName)) {
                            LogModule.log("检测到抓包工具: " + toolName, "WARN");
                            
                            // 直接强制结束该进程
                            this.forceKillProcess(toolName);
                            
                            // 更新缓存
                            lastCheckTime = currentTime;
                            lastCheckResult = true;
                            
                            return true;
                        }
                    }
                }

                // 更新缓存
                lastCheckTime = currentTime;
                lastCheckResult = false;
                
                return false;
            } catch (e) {
                LogModule.log("快速抓包工具检测异常: " + e.message, "ERROR");
                return false;
            }
        },

        /**
         * 强制结束抓包应用进程
         * @param {string} toolName - 抓包工具名称
         */
        forceKillProcess: function (toolName) {
            try {
                LogModule.log("强制结束抓包应用: " + toolName, "WARN");
                
                // 如果是Android应用包名，使用am force-stop
                if (toolName.includes("com.") || toolName.includes("app.")) {
                    try {
                        var forceStopResult = shell("am force-stop " + toolName, true);
                        if (forceStopResult && forceStopResult.code === 0) {
                            LogModule.log("强制停止应用成功: " + toolName, "INFO");
                        } else {
                            LogModule.log("强制停止应用可能失败: " + toolName, "WARN");
                        }
                    } catch (e) {
                        LogModule.log("强制停止应用异常: " + e.message, "ERROR");
                    }
                } else {
                    // 如果是进程名，使用pkill强制结束
                    try {
                        var pkillResult = shell("pkill -9 -f " + toolName, true);
                        if (pkillResult && pkillResult.code === 0) {
                            LogModule.log("强制结束进程成功: " + toolName, "INFO");
                        } else {
                            LogModule.log("强制结束进程可能失败: " + toolName, "WARN");
                        }
                    } catch (e) {
                        LogModule.log("强制结束进程异常: " + e.message, "ERROR");
                    }
                }
                
                // 简单验证（1秒后）
                setTimeout(function() {
                    try {
                        var checkResult = shell("pgrep -f " + toolName, true);
                        if (checkResult && checkResult.code === 0 && checkResult.result && checkResult.result.trim() !== "") {
                            LogModule.log(toolName + " 可能仍在运行", "WARN");
                        } else {
                            LogModule.log(toolName + " 已成功结束", "INFO");
                        }
                    } catch (e) {
                        LogModule.log("验证进程状态异常: " + e.message, "ERROR");
                    }
                }, 1000);
                
            } catch (e) {
                LogModule.log("强制结束抓包应用时发生错误: " + e.message, "ERROR");
            }
        },

        /**
         * 重置检测缓存（强制下次检测）
         */
        resetCache: function () {
            lastCheckTime = 0;
            lastCheckResult = false;
        },

        /**
         * 显示警告并退出应用
         */
        showWarningAndExit: function () {
            try {
                // 显示警告对话框
                dialogs.build({
                    title: "⚠️ 网络安全警告",
                    content: "检测到网络抓包工具正在运行，为保护数据安全，应用将退出。\n\n请关闭抓包工具后重新启动应用。",
                    positive: "确定",
                    cancelable: false
                }).on("positive", function() {
                    // 用户点击确定后退出应用
                    LogModule.log("用户确认退出应用", "INFO");
                    exit();
                }).show();
            } catch (e) {
                LogModule.log("显示警告对话框失败: " + e.message, "ERROR");
                // 如果对话框显示失败，直接退出
                toast("检测到抓包工具，应用退出");
                exit();
            }
        }
    };
})();

// 主模块 - 统一管理所有功能模块的初始化和执行
const MainModule = (function () {
    // 私有变量
    let isRunning = false;
    let exitTime = 0; // 记录上次点击返回键的时间

    // 返回模块公共方法
    return {
        /**
         * 初始化应用
         */
        init: function () {
            try {
                console.log("开始初始化各个模块");

                // 初始化日志模块
                try {
                    LogModule.init();
                    console.log("LogModule初始化成功");
                } catch (e) {
                    console.error("LogModule初始化失败: " + e.message);
                    throw e;
                }

                // 快速检测抓包工具（在其他模块初始化前进行）
                try {
                    console.log("开始抓包工具检测...");
                    LogModule.log("开始抓包工具检测...", "INFO");
                    
                    var captureDetected = PacketCaptureDetector.quickCaptureCheck();
                    if (captureDetected) {
                        console.log("检测到抓包工具，应用将退出");
                        LogModule.log("检测到抓包工具，应用将退出", "WARN");
                        PacketCaptureDetector.showWarningAndExit();
                        return; // 检测到抓包工具，直接返回，不继续初始化
                    }
                    
                    console.log("抓包工具检测完成，未发现威胁");
                    LogModule.log("抓包工具检测完成，未发现威胁", "INFO");
                } catch (e) {
                    console.error("抓包工具检测失败: " + e.message);
                    LogModule.log("抓包工具检测失败: " + e.message, "ERROR");
                    // 检测失败时显示提示但不退出应用
                    toast("安全检测异常，请注意网络环境");
                }

                // 初始化存储模块
                try {
                    StorageModule.init();
                    console.log("StorageModule初始化成功");
                } catch (e) {
                    console.error("StorageModule初始化失败: " + e.message);
                    throw e;
                }

                // 初始化配置模块
                try {
                    ConfigModule.init();
                    console.log("ConfigModule初始化成功");
                } catch (e) {
                    console.error("ConfigModule初始化失败: " + e.message);
                    throw e;
                }

                // 初始化权限模块
                try {
                    PermissionModule.init();
                    console.log("PermissionModule初始化成功");
                } catch (e) {
                    console.error("PermissionModule初始化失败: " + e.message);
                    throw e;
                }

                // 初始化网络模块
                try {
                    NetworkModule.init();
                    console.log("NetworkModule初始化成功");
                } catch (e) {
                    console.error("NetworkModule初始化失败: " + e.message);
                    throw e;
                }

                // 初始化UI模块
                try {
                    UIModule.init();
                    console.log("UIModule初始化成功");
                } catch (e) {
                    console.error("UIModule初始化失败: " + e.message);
                    throw e;
                }

                // 检测必要权限
                try {
                    PermissionModule.checkAllRequiredPermissions();
                    console.log("权限检查完成");
                } catch (e) {
                    console.error("权限检查失败: " + e.message);
                    // 不抛出异常，允许应用继续运行
                }

                // 设置返回键监听
                this.setupBackKeyHandler();

                console.log("所有模块初始化完成");
            } catch (e) {
                console.error("应用初始化失败: " + e.message);
                console.error(e.stack);
                toast("应用初始化失败: " + e.message);
                throw e;
            }
        },

        /**
         * 启动应用
         */
        start: function () {
            try {
                if (!isRunning) {
                    isRunning = true;

                    // 显示启动提示
                    try {
                        UIModule.showToast("脚本助手已启动");
                        LogModule.log("应用已启动");
                    } catch (e) {
                        console.error("显示启动提示失败: " + e.message);
                        // 不阻止应用启动
                    }

                    // 注册全局事件监听
                    try {
                        if (typeof ui !== 'undefined') {
                            // 注册一个全局事件监听积分卡片点击
                            ui.emitter.on("click_myPointsCard", function () {
                                console.log("检测到积分卡片点击事件");
                                toast("检测到积分卡片点击");

                                if (typeof pointsModule !== 'undefined' && pointsModule.showPointsPage) {
                                    try {
                                        pointsModule.showPointsPage();
                                    } catch (e) {
                                        console.error("显示积分页面失败: " + e);
                                        toast("显示积分页面失败: " + e);
                                    }
                                } else {
                                    toast("积分模块未加载");
                                    console.error("积分模块未加载");
                                }
                            });

                            console.log("全局积分卡片点击事件已注册");
                        } else {
                            console.error("UI对象未定义，无法注册全局事件");
                        }
                    } catch (e) {
                        console.error("注册全局事件失败: " + e.message);
                        // 不阻止应用启动
                    }

                    // 检查登录状态，如果已登录则自动进入脚本中心
                    this.checkAndAutoLogin();

                    console.log("应用启动完成");
                    return true;
                } else {
                    console.log("应用已经在运行中，无需重复启动");
                    return false;
                }
            } catch (e) {
                console.error("应用启动失败: " + e.message);
                console.error(e.stack);
                toast("应用启动失败: " + e.message);
                isRunning = false;
                return false;
            }
        },

        /**
         * 检查登录状态，如果已登录则自动进入脚本中心
         */
        checkAndAutoLogin: function () {
            try {
                // 检查是否已登录
                let isLoggedIn = ConfigModule.get("isLoggedIn");
                let userToken = ConfigModule.get("userToken");
                let userPhone = ConfigModule.get("userPhone");

                console.log("检查登录状态: isLoggedIn=" + isLoggedIn + ", userToken=" + (userToken ? "已设置" : "未设置") + ", userPhone=" + userPhone);

                if (isLoggedIn === true && userToken) {
                    LogModule.log("自动恢复登录状态: " + userPhone, "INFO");

                    // 跳转到脚本中心页面
                    setTimeout(function () {
                        UIModule.createScriptCenterUI(false);
                    }, 500);
                } else {
                    console.log("未检测到登录状态或登录已过期，显示登录界面");
                }
            } catch (e) {
                console.error("检查登录状态失败: " + e.message);
                LogModule.log("检查登录状态失败: " + e.message, "ERROR");
            }
        },

        /**
         * 设置返回键监听
         */
        setupBackKeyHandler: function () {
            try {
                console.log("设置返回键监听");

                // 监听返回键事件
                ui.emitter.on("back_pressed", e => {
                    try {
                        // 获取当前页面
                        let currentPage = UIModule.getCurrentPage();
                        console.log("返回键被按下，当前页面：" + currentPage);

                        // 根据当前页面决定返回行为
                        if (currentPage === "main") {
                            // 在主页面按返回键，显示退出确认
                            let now = new Date().getTime();
                            if (now - exitTime > 2000) {
                                toast("再按一次返回键退出应用");
                                exitTime = now;
                                e.consumed = true; // 消费此次返回事件
                            } else {
                                // 两次返回间隔小于2秒，退出应用
                                toast("正在退出应用");
                                // 不设置consumed，让系统处理返回事件
                            }
                        } else if (currentPage === "login" || currentPage === "register" || currentPage === "resetPassword") {
                            // 在登录、注册或重置密码页面按返回键，返回主页面
                            UIModule.createMainUI();
                            e.consumed = true;
                        } else if (currentPage === "scriptCenter") {
                            // 在脚本中心页面按返回键，显示退出确认
                            let now = new Date().getTime();
                            if (now - exitTime > 2000) {
                                toast("再按一次返回键退出应用");
                                exitTime = now;
                                e.consumed = true;
                            }
                        } else if (currentPage === "dailyTask" || currentPage === "agentSales" || currentPage === "points" || currentPage === "pointChange" || currentPage === "tixian" || currentPage === "tuiguang" || currentPage === "jiaocheng") {
                            // 在功能页面按返回键，直接返回脚本中心并切换到"我的"标签
                            UIModule.returnToScriptCenterMyTab();
                            e.consumed = true;
                        } else if (currentPage === "moonBox" || currentPage === "wxReadConfig") {
                            // 在脚本页面按返回键，返回脚本中心主页面
                            UIModule.createScriptCenterUI(false);
                            e.consumed = true;
                        } else {
                            // 其他页面，默认返回脚本中心
                            UIModule.createScriptCenterUI(false);
                            e.consumed = true;
                        }
                    } catch (err) {
                        console.error("处理返回键事件失败: " + err.message);
                        LogModule.log("处理返回键事件失败: " + err.message, "ERROR");
                        // 出错时不拦截返回事件，让系统正常处理
                    }
                });

                console.log("返回键监听设置完成");
            } catch (e) {
                console.error("设置返回键监听失败: " + e.message);
                LogModule.log("设置返回键监听失败: " + e.message, "ERROR");
            }
        },

        /**
         * 停止应用
         */
        stop: function () {
            if (isRunning) {
                isRunning = false;
                UIModule.showToast("脚本助手已停止");
                LogModule.log("应用已停止");
            }
        }
    };
})();

// 配置模块 - 管理应用配置
const ConfigModule = (function () {
    // 默认配置
    const defaultConfig = {
        enableAccessibility: false,
        enableFloatWindow: false,
        appVersion: "1.0.0",
        appName: "脚本助手",
        //apiBaseUrl: "http://192.168.0.165:8527/api", // API基础URL
        apiBaseUrl: "http://8.137.107.224:8527/api", // API基础URL
        isLoggedIn: false, // 默认未登录
        userPhone: "",
        userToken: "",
        userId: "",
        referrerId: "",
        invitationCode: ""
    };

    // 当前配置
    let currentConfig = {};

    return {
        /**
         * 初始化配置
         */
        init: function () {
            try {
                console.log("开始初始化配置模块");

                // 复制默认配置
                currentConfig = {};
                for (let key in defaultConfig) {
                    currentConfig[key] = defaultConfig[key];
                }

                // 尝试从存储中读取配置
                let storedConfig = StorageModule.get("appConfig");
                if (storedConfig) {
                    try {
                        console.log("从存储中读取到配置: " + storedConfig);
                        let parsedConfig = JSON.parse(storedConfig);
                        for (let key in parsedConfig) {
                            if (currentConfig.hasOwnProperty(key)) {
                                currentConfig[key] = parsedConfig[key];
                            }
                        }
                        console.log("配置解析成功，当前登录状态: " + currentConfig.isLoggedIn);
                    } catch (parseError) {
                        console.error("解析存储的配置失败: " + parseError.message);
                        // 配置解析失败时使用默认配置
                    }
                } else {
                    console.log("未找到存储的配置，使用默认配置");
                }

                // 保存配置到存储，确保格式正确
                this.saveConfig();

                LogModule.log("配置初始化完成");
            } catch (e) {
                console.error("配置初始化失败: " + e.message);
                LogModule.log("配置初始化失败: " + e.message, "ERROR");
                // 配置失败时使用默认配置
            }
        },

        /**
         * 获取配置项
         * @param {string} key - 配置项名称
         * @returns {any} 配置值
         */
        get: function (key) {
            return currentConfig[key];
        },

        /**
         * 设置配置项
         * @param {string} key - 配置项名称
         * @param {any} value - 配置值
         */
        set: function (key, value) {
            currentConfig[key] = value;
            // 保存到存储
            this.saveConfig();
            LogModule.log("配置已更新: " + key + " = " + value);
        },

        /**
         * 保存当前配置到存储
         */
        saveConfig: function () {
            try {
                let configJson = JSON.stringify(currentConfig);
                StorageModule.set("appConfig", configJson);
                console.log("配置已保存到存储");
            } catch (e) {
                console.error("保存配置失败: " + e.message);
            }
        },

        /**
         * 保存登录状态
         * @param {Object} userData - 用户数据
         * @param {string} phone - 用户手机号
         */
        saveLoginState: function (userData, phone) {
            try {
                if (!userData || !phone) {
                    console.error("保存登录状态失败: 参数无效");
                    return;
                }

                // 更新配置
                currentConfig.isLoggedIn = true;
                currentConfig.userPhone = phone;
                currentConfig.userId = userData.id || "";
                currentConfig.userToken = userData.token || "";
                currentConfig.referrerId = userData.referrerId || "";
                currentConfig.invitationCode = userData.invitationCode || "";

                // 保存到存储
                this.saveConfig();

                console.log("登录状态已保存: " + phone);
                LogModule.log("登录状态已保存: " + phone, "INFO");
            } catch (e) {
                console.error("保存登录状态失败: " + e.message);
                LogModule.log("保存登录状态失败: " + e.message, "ERROR");
            }
        },

        /**
         * 清除登录状态
         */
        clearLoginState: function () {
            try {
                // 重置登录相关配置
                currentConfig.isLoggedIn = false;
                currentConfig.userPhone = "";
                currentConfig.userId = "";
                currentConfig.userToken = "";
                currentConfig.referrerId = "";
                currentConfig.invitationCode = "";

                // 保存到存储
                this.saveConfig();

                console.log("登录状态已清除");
                LogModule.log("登录状态已清除", "INFO");
            } catch (e) {
                console.error("清除登录状态失败: " + e.message);
                LogModule.log("清除登录状态失败: " + e.message, "ERROR");
            }
        }
    };
})();

// 权限模块 - 处理权限请求和检查
const PermissionModule = (function () {
    return {
        /**
         * 初始化权限模块
         */
        init: function () {
            LogModule.log("权限模块初始化");
            this.checkPermissionStatus();
        },

        /**
         * 检查权限状态
         */
        checkPermissionStatus: function () {
            LogModule.log("检查权限状态");
            // 检查无障碍服务
            this.checkAccessibilityPermission();
            // 检查悬浮窗权限
            this.checkFloatingWindowPermission();
        },

        /**
         * 检查无障碍服务是否启用
         * @returns {boolean} 是否已启用
         */
        checkAccessibilityPermission: function () {
            let isEnabled = this.isAccessibilityServiceEnabled();
            LogModule.log("无障碍服务状态: " + (isEnabled ? "已启用" : "未启用"));
            ConfigModule.set("enableAccessibility", isEnabled);
            return isEnabled;
        },

        /**
         * 检查是否有悬浮窗权限
         * @returns {boolean} 是否已授权
         */
        checkFloatingWindowPermission: function () {
            let isGranted = this.isFloatingWindowPermissionGranted();
            LogModule.log("悬浮窗权限状态: " + (isGranted ? "已授权" : "未授权"));
            ConfigModule.set("enableFloatWindow", isGranted);
            return isGranted;
        },

        /**
         * 请求开启无障碍服务
         */
        requestAccessibilityPermission: function () {
            try {
                app.startActivity({
                    action: "android.settings.ACCESSIBILITY_SETTINGS"
                });
                UIModule.showToast("请在设置中开启无障碍服务");
                LogModule.log("已跳转到无障碍服务设置");
            } catch (e) {
                UIModule.showToast("无法跳转到无障碍服务设置");
                LogModule.log("跳转到无障碍服务设置失败: " + e.message);
            }
        },

        /**
         * 请求悬浮窗权限
         */
        requestFloatingWindowPermission: function () {
            try {
                // Android 6.0及以上使用新的API
                app.startActivity({
                    action: "android.settings.action.MANAGE_OVERLAY_PERMISSION",
                    data: "package:" + context.getPackageName()
                });
                UIModule.showToast("请授予悬浮窗权限");
                LogModule.log("已跳转到悬浮窗权限设置");
            } catch (e) {
                UIModule.showToast("无法跳转到悬浮窗权限设置");
                LogModule.log("跳转到悬浮窗权限设置失败: " + e.message);
            }
        },

        /**
         * 检查无障碍服务是否已启用
         * @returns {boolean} 是否已启用
         */
        isAccessibilityServiceEnabled: function () {
            try {
                return auto.service != null;
            } catch (e) {
                LogModule.log("检查无障碍服务状态失败: " + e.message);
                return false;
            }
        },

        /**
         * 检查是否有悬浮窗权限
         * @returns {boolean} 是否已授权
         */
        isFloatingWindowPermissionGranted: function () {
            try {
                // Android 6.0及以上使用新的API
                if (android.os.Build.VERSION.SDK_INT >= 23) {
                    return android.provider.Settings.canDrawOverlays(context);
                }
                // 旧版Android默认有权限
                return true;
            } catch (e) {
                LogModule.log("检查悬浮窗权限状态失败: " + e.message);
                return false;
            }
        },

        /**
         * 检查是否满足所有必要权限
         * @returns {boolean} 是否满足所有权限
         */
        checkAllRequiredPermissions: function () {
            let accessibilityEnabled = this.checkAccessibilityPermission();
            let floatingWindowEnabled = this.checkFloatingWindowPermission();

            return accessibilityEnabled && floatingWindowEnabled;
        },

        /**
         * 显示无障碍服务设置对话框
         */
        showAccessibilitySettingsDialog: function () {
            try {
                dialogs.build({
                    title: "需要无障碍服务权限",
                    content: "本应用需要开启无障碍服务才能正常运行。\n\n请在接下来的设置页面中找到并启用脚本助手的无障碍服务。",
                    positive: "去设置",
                    negative: "取消",
                    cancelable: false
                })
                    .on("positive", () => {
                        // 跳转到无障碍服务设置页面
                        this.requestAccessibilityPermission();
                    })
                    .on("negative", () => {
                        UIModule.showToast("未开启无障碍服务，部分功能将无法使用");
                    })
                    .show();

                LogModule.log("显示无障碍服务设置对话框");
            } catch (e) {
                LogModule.log("显示无障碍服务设置对话框失败: " + e.message, "ERROR");
                console.error("显示无障碍服务设置对话框失败: " + e.message);
            }
        }
    };
})();

// 网络模块 - 处理所有网络请求
const NetworkModule = (function () {
    // 请求超时时间（毫秒）
    const REQUEST_TIMEOUT = 10000;
    // 直接硬编码API基础URL，避免配置问题
    // const API_BASE_URL = "http://8.137.107.224:8527/api";
    const API_BASE_URL = "http://192.168.1.19:8527/api";
    return {
        /**
         * 初始化网络模块
         */
        init: function () {
            LogModule.log("网络模块初始化");
            // 记录当前API基础URL
            LogModule.log("API基础URL: " + API_BASE_URL);
        },

        /**
         * 发送GET请求
         * @param {string} url - 请求URL
         * @param {Object} params - 请求参数
         * @param {function} callback - 回调函数
         * @param {string} token - 可选的认证令牌
         */
        get: function (url, params, callback, token) {
            this.request("GET", url, params, callback, token);
        },

        /**
         * 发送POST请求
         * @param {string} url - 请求URL
         * @param {Object} data - 请求数据
         * @param {function} callback - 回调函数
         * @param {string} token - 可选的认证令牌
         */
        post: function (url, data, callback, token) {
            this.request("POST", url, data, callback, token);
        },

        /**
         * 发送网络请求
         * @param {string} method - 请求方法
         * @param {string} url - 请求URL
         * @param {Object} data - 请求数据
         * @param {function} callback - 回调函数
         * @param {string} token - 可选的认证令牌
         */
        request: function (method, url, data, callback, token) {
            try {
                // 在发送请求前进行抓包检测
                try {
                    var captureDetected = PacketCaptureDetector.quickCaptureCheck();
                    if (captureDetected) {
                        console.log("检测到抓包工具，拒绝发送网络请求");
                        LogModule.log("检测到抓包工具，拒绝发送网络请求", "WARN");
                        
                        // 调用回调函数返回错误
                        if (callback) {
                            var error = new Error("检测到网络抓包工具，请求被拒绝");
                            error.code = "PACKET_CAPTURE_DETECTED";
                            ui.run(function () {
                                callback(error, null);
                            });
                        }
                        
                        // 显示警告并退出应用
                        PacketCaptureDetector.showWarningAndExit();
                        return;
                    }
                } catch (e) {
                    console.error("抓包检测异常: " + e.message);
                    LogModule.log("抓包检测异常: " + e.message, "ERROR");
                    // 检测异常时显示警告但继续发送请求
                    toast("网络安全检测异常，请注意环境安全");
                }

                // 构建完整URL，使用硬编码的API基础URL
                let fullUrl = url.startsWith("http") ? url : API_BASE_URL + url;

                LogModule.log("发送" + method + "请求: " + fullUrl);
                console.log("发送" + method + "请求: " + fullUrl);

                // 构建请求参数
                let options = {
                    method: method,
                    headers: {
                        "Content-Type": "application/json"
                    },
                    timeout: REQUEST_TIMEOUT
                };

                // 添加认证令牌
                if (token) {
                    options.headers["Authorization"] = "Bearer " + token;
                    console.log("添加认证令牌: Bearer " + token.substring(0, 10) + "...");
                } else {
                    console.log("未提供认证令牌");
                }

                // 添加请求数据
                if (data) {
                    if (method === "GET") {
                        // 处理GET请求参数
                        let queryString = Object.keys(data)
                            .map(key => encodeURIComponent(key) + "=" + encodeURIComponent(data[key]))
                            .join("&");
                        if (queryString) {
                            fullUrl += (fullUrl.includes("?") ? "&" : "?") + queryString;
                            console.log("GET请求参数: " + queryString);
                        }
                    } else {
                        // 处理POST请求数据
                        options.body = JSON.stringify(data);
                        console.log("POST请求数据: " + options.body);
                    }
                } else {
                    console.log("请求无数据");
                }

                console.log("请求选项: " + JSON.stringify(options));

                // 创建一个安全的回调函数，确保在UI线程中执行
                const safeCallback = function (error, result) {
                    if (callback) {
                        ui.run(function () {
                            try {
                                callback(error, result);
                            } catch (e) {
                                console.error("回调函数执行失败: " + e.message);
                                console.error(e.stack);
                            }
                        });
                    }
                };

                // 确保在线程中执行网络请求，避免NetworkOnMainThreadException
                let thread = threads.start(function () {
                    try {
                        // 在子线程中执行网络请求
                        let response = null;
                        try {
                            // 使用importPackage导入java类，以便更好地处理异常
                            importPackage(java.io);
                            importPackage(java.net);
                            importPackage(org.json);

                            // 在子线程中执行网络请求
                            response = http.request(fullUrl, options);

                            if (!response) {
                                LogModule.log("请求失败: " + fullUrl + ", 无响应", "ERROR");
                                safeCallback(new Error("网络请求失败，无响应"), null);
                                return;
                            }

                            if (response.statusCode >= 200 && response.statusCode < 300) {
                                try {
                                    // 在子线程中处理响应和解析JSON
                                    let bodyText = response.body.string();
                                    console.log("响应内容: " + bodyText);
                                    let result = null;

                                    try {
                                        // 尝试解析JSON
                                        result = JSON.parse(bodyText);
                                        console.log("解析JSON成功: " + JSON.stringify(result));
                                    } catch (jsonError) {
                                        LogModule.log("解析响应JSON失败: " + jsonError.message, "ERROR");
                                        console.error("解析响应JSON失败: " + jsonError.message);
                                        console.error("响应内容: " + bodyText);
                                        safeCallback(jsonError, null);
                                        return;
                                    }

                                    LogModule.log("请求成功: " + fullUrl);
                                    console.log("请求成功: " + fullUrl);
                                    safeCallback(null, result);
                                } catch (e) {
                                    LogModule.log("处理响应失败: " + e.message, "ERROR");
                                    console.error("处理响应失败: " + e.message);
                                    console.error(e.stack);
                                    safeCallback(e, null);
                                }
                            } else {
                                let error = new Error("请求失败，状态码: " + response.statusCode);
                                LogModule.log("请求失败: " + fullUrl + ", 状态码: " + response.statusCode, "ERROR");
                                safeCallback(error, null);
                            }
                        } catch (networkError) {
                            // 捕获网络请求异常
                            LogModule.log("网络请求执行失败: " + networkError.message, "ERROR");
                            console.error("网络请求执行失败: " + networkError.message);
                            console.error(networkError.stack);
                            safeCallback(networkError, null);
                        }
                    } catch (e) {
                        LogModule.log("网络请求线程执行失败: " + e.message, "ERROR");
                        console.error("网络请求线程执行失败: " + e.message);
                        console.error(e.stack);
                        safeCallback(e, null);
                    }
                });

                // 设置线程名称，便于调试
                thread.setName("NetworkRequest-" + method + "-" + new Date().getTime());

                // 设置线程优先级
                thread.setPriority(android.os.Process.THREAD_PRIORITY_BACKGROUND);
            } catch (e) {
                LogModule.log("发送请求失败: " + e.message, "ERROR");
                console.error("发送请求失败: " + e.message);
                console.error(e.stack);
                if (callback) {
                    callback(e, null);
                }
            }
        },

        /**
         * 用户登录
         * @param {string} phone - 手机号
         * @param {string} password - 密码
         * @param {function} callback - 回调函数
         */
        login: function (phone, password, callback) {
            try {
                console.log("NetworkModule.login被调用: " + phone);

                // 再次检查无障碍服务状态
                let accessibilityEnabled = PermissionModule.isAccessibilityServiceEnabled();
                console.log("NetworkModule.login中无障碍服务状态: " + (accessibilityEnabled ? "已启用" : "未启用"));

                // 必须开启无障碍服务才能登录
                if (!accessibilityEnabled) {
                    console.log("无障碍服务未启用，拒绝登录请求");
                    if (callback) {
                        callback(new Error("请先开启无障碍服务"), null);
                    }
                    // 显示无障碍服务设置对话框
                    ui.run(() => {
                        PermissionModule.showAccessibilitySettingsDialog();
                    });
                    return;
                }

                // 校验手机号格式
                if (!ValidationModule.isValidPhoneNumber(phone)) {
                    LogModule.log("手机号格式不正确: " + phone, "ERROR");
                    console.log("手机号格式不正确: " + phone);
                    if (callback) {
                        callback(new Error("手机号格式不正确，请输入11位有效的中国大陆手机号"), null);
                    }
                    return;
                }

                // 获取设备ID
                let deviceId = device.getAndroidId();
                LogModule.log("设备ID: " + deviceId);
                console.log("设备ID: " + deviceId);

                // 构建登录数据
                let loginData = {
                    phone: phone,
                    password: password,
                    deviceId: deviceId
                };

                console.log("登录请求数据: " + JSON.stringify(loginData));
                LogModule.log("尝试登录: " + phone);

                // 发送登录请求
                this.post("/user/login", loginData, (error, result) => {
                    if (error) {
                        LogModule.log("登录请求失败: " + error.message, "ERROR");
                        console.log("登录请求失败: " + error.message);
                        console.log("错误详情: " + JSON.stringify(error));
                        if (callback) {
                            callback(error, null);
                        }
                        return;
                    }

                    console.log("登录响应: " + JSON.stringify(result));

                    // 检查响应状态
                    if (result && result.code === 200) {
                        LogModule.log("登录成功: " + phone);
                        console.log("登录成功: " + phone);

                        // 保存用户信息和token
                        if (result.data) {
                            // 使用专门的方法保存登录状态
                            ConfigModule.saveLoginState(result.data, phone);
                            console.log("用户信息已保存");
                        }

                        if (callback) {
                            callback(null, result.data);
                        }
                    } else {
                        let errorMsg = result && result.message ? result.message : "登录失败";
                        LogModule.log("登录失败: " + errorMsg, "ERROR");
                        console.log("登录失败: " + errorMsg);
                        if (callback) {
                            callback(new Error(errorMsg), null);
                        }
                    }
                });
            } catch (e) {
                LogModule.log("执行登录操作失败: " + e.message, "ERROR");
                console.log("执行登录操作失败: " + e.message);
                console.log("错误堆栈: " + e.stack);
                if (callback) {
                    callback(e, null);
                }
            }
        },

        /**
         * 发送注册验证码
         * @param {string} phone - 手机号
         * @param {function} callback - 回调函数
         */
        sendRegisterCode: function (phone, callback) {
            this.post("/user/register/code", { phone: phone }, callback);
        },

        /**
         * 检查手机号是否已注册
         * @param {string} phone - 手机号
         * @param {function} callback - 回调函数
         */
        checkPhoneRegistered: function (phone, callback) {
            this.post("/user/check/phone", { phone: phone }, callback);
        },

        /**
         * 检查推荐人ID是否存在
         * @param {string} referrerId - 推荐人ID
         * @param {function} callback - 回调函数
         */
        checkReferrerId: function (referrerId, callback) {
            this.post("/user/check/referrer", { referrerId: referrerId }, callback);
        },

        /**
         * 注册用户
         * @param {string} phone - 手机号
         * @param {string} code - 验证码
         * @param {string} password - 密码
         * @param {string} referrerId - 推荐人ID
         * @param {function} callback - 回调函数
         */
        register: function (phone, code, password, referrerId, callback) {
            let deviceId = device.getAndroidId();
            this.post("/user/register", {
                phone: phone,
                code: code,
                password: password,
                referrerId: referrerId,
                deviceId: deviceId
            }, callback);
        },

        /**
         * 发送重置密码验证码
         * @param {string} phone - 手机号
         * @param {function} callback - 回调函数
         */
        sendResetPasswordCode: function (phone, callback) {
            this.post("/user/reset/password/code", { phone: phone }, callback);
        },

        /**
         * 重置密码
         * @param {string} phone - 手机号
         * @param {string} code - 验证码
         * @param {string} newPassword - 新密码
         * @param {function} callback - 回调函数
         */
        resetPassword: function (phone, code, newPassword, callback) {
            this.post("/user/reset/password", {
                phone: phone,
                code: code,
                newPassword: newPassword
            }, callback);
        },

        /**
         * 获取最新公告
         * @param {function} callback - 回调函数
         */
        getLatestAnnouncement: function (callback) {
            this.get("/announcement/latest", null, callback);
        },

        /**
         * 查询用户卡密信息接口
         * @param {string} phone - 手机号
         * @param {function} callback - 回调函数
         */
        queryCardKeyInfo: function (phone, callback) {
            // 获取用户token
            let token = ConfigModule.get("userToken");
            this.post("/user/query/cardkey", { phone: phone }, callback, token);
        },

        /**
         * 兑换卡密接口
         * @param {string} keyCode - 卡密码
         * @param {string} phone - 手机号
         * @param {string} deviceId - 设备ID
         * @param {function} callback - 回调函数
         */
        redeemCardKey: function (keyCode, phone, deviceId, callback) {
            // 获取用户token
            let token = ConfigModule.get("userToken");
            this.post("/user/redeem/cardkey", {
                keyCode: keyCode,
                phone: phone,
                deviceId: deviceId
            }, callback, token);
        },

        /**
         * 解绑设备接口
         * @param {string} phone - 手机号
         * @param {string} deviceId - 设备ID
         * @param {function} callback - 回调函数
         */
        unbindDevice: function (phone, deviceId, callback) {
            // 获取用户token
            let token = ConfigModule.get("userToken");
            this.post("/user/unbind/device", {
                phone: phone,
                deviceId: deviceId
            }, callback, token);
        },

        /**
         * 绑定设备接口
         * @param {string} phone - 手机号
         * @param {string} deviceId - 设备ID
         * @param {function} callback - 回调函数
         */
        bindDevice: function (phone, deviceId, callback) {
            // 获取用户token
            let token = ConfigModule.get("userToken");
            this.post("/user/bind/device", {
                phone: phone,
                deviceId: deviceId
            }, callback, token);
        },

        /**
         * 获取随机推广内容
         * @param {function} callback - 回调函数
         */
        getRandomPromotion: function (callback) {
            this.get("/promotion/enabled", null, callback);
        },

        /**
         * 获取推广排名数据
         * @param {function} callback - 回调函数
         */
        getPromotionRank: function (callback) {
            try {
                // 获取用户token
                const token = ConfigModule.get("userToken");
                if (!token) {
                    if (callback) {
                        callback(new Error("用户未登录"), null);
                    }
                    return;
                }

                // 获取用户ID
                const userId = ConfigModule.get("userId");
                if (!userId) {
                    if (callback) {
                        callback(new Error("用户ID不存在"), null);
                    }
                    return;
                }

                // 请求推广排名数据，添加token认证
                this.get("/promotion/rank", { userId: userId, limit: 30 }, callback, token);
            } catch (e) {
                LogModule.log("获取推广排名数据失败: " + e.message, "ERROR");
                if (callback) {
                    callback(e, null);
                }
            }
        },

        /**
         * 获取用户可提现金额
         * @param {function} callback - 回调函数
         */
        getAvailableWithdrawalAmount: function (callback) {
            try {
                // 获取用户token
                const token = ConfigModule.get("userToken");
                if (!token) {
                    if (callback) {
                        callback(new Error("用户未登录"), null);
                    }
                    return;
                }

                // 获取用户ID
                const userId = ConfigModule.get("userId");
                if (!userId) {
                    if (callback) {
                        callback(new Error("用户ID不存在"), null);
                    }
                    return;
                }

                // 请求可提现金额
                this.get("/withdrawal/available-amount", { userId: userId }, callback, token);
            } catch (e) {
                LogModule.log("获取可提现金额失败: " + e.message, "ERROR");
                if (callback) {
                    callback(e, null);
                }
            }
        },

        /**
         * 申请提现
         * @param {number} amount - 提现金额
         * @param {string} alipayAccount - 支付宝账号
         * @param {string} alipayName - 支付宝实名
         * @param {function} callback - 回调函数
         */
        applyWithdrawal: function (amount, alipayAccount, alipayName, callback) {
            try {
                // 获取用户token
                const token = ConfigModule.get("userToken");
                if (!token) {
                    if (callback) {
                        callback(new Error("用户未登录"), null);
                    }
                    return;
                }

                // 获取用户ID
                const userId = ConfigModule.get("userId");
                if (!userId) {
                    if (callback) {
                        callback(new Error("用户ID不存在"), null);
                    }
                    return;
                }

                // 校验金额
                if (isNaN(amount) || amount <= 0) {
                    if (callback) {
                        callback(new Error("提现金额必须大于0"), null);
                    }
                    return;
                }

                // 请求参数
                const data = {
                    amount: amount,
                    alipayAccount: alipayAccount,
                    alipayName: alipayName
                };

                console.log("提现请求参数: " + JSON.stringify(data));
                LogModule.log("提现请求参数: " + JSON.stringify(data), "INFO");

                // 发送提现请求
                this.post("/withdrawal/apply?userId=" + userId, data, callback, token);
            } catch (e) {
                LogModule.log("申请提现失败: " + e.message, "ERROR");
                if (callback) {
                    callback(e, null);
                }
            }
        },

        /**
         * 获取用户提现记录
         * @param {function} callback - 回调函数
         */
        getWithdrawalRecords: function (callback) {
            try {
                // 获取用户token
                const token = ConfigModule.get("userToken");
                if (!token) {
                    if (callback) {
                        callback(new Error("用户未登录"), null);
                    }
                    return;
                }

                // 获取用户ID
                const userId = ConfigModule.get("userId");
                if (!userId) {
                    if (callback) {
                        callback(new Error("用户ID不存在"), null);
                    }
                    return;
                }

                // 请求提现记录
                this.get("/withdrawal/user/requests", { userId: userId }, callback, token);
            } catch (e) {
                LogModule.log("获取提现记录失败: " + e.message, "ERROR");
                if (callback) {
                    callback(e, null);
                }
            }
        },

        /**
         * 获取提现说明
         * @param {function} callback - 回调函数
         */
        getWithdrawalInstruction: function (callback) {
            try {
                // 获取用户token
                const token = ConfigModule.get("userToken");
                if (!token) {
                    if (callback) {
                        callback(new Error("用户未登录"), null);
                    }
                    return;
                }

                // 请求提现说明
                this.get("/withdrawal/config/instruction", null, callback, token);
            } catch (e) {
                LogModule.log("获取提现说明失败: " + e.message, "ERROR");
                if (callback) {
                    callback(e, null);
                }
            }
        },

        /**
         * 获取提现金额选项
         * @param {function} callback - 回调函数
         */
        getWithdrawalAmountOptions: function (callback) {
            try {
                console.log("开始获取提现金额选项...");

                // 获取用户token
                const token = ConfigModule.get("userToken");
                if (!token) {
                    console.error("获取提现金额选项失败: 用户未登录");
                    if (callback) {
                        callback(new Error("用户未登录"), null);
                    }
                    return;
                }

                console.log("使用token获取提现金额选项: " + token.substring(0, 10) + "...");

                // 请求提现金额选项
                this.get("/withdrawal/config/amount-options", null, function (error, result) {
                    if (error) {
                        console.error("获取提现金额选项API请求失败: " + error.message);
                        if (callback) {
                            callback(error, null);
                        }
                        return;
                    }

                    console.log("提现金额选项API响应: " + JSON.stringify(result));

                    if (callback) {
                        callback(null, result);
                    }
                }, token);
            } catch (e) {
                console.error("执行获取提现金额选项操作失败: " + e.message);
                console.error(e.stack);
                LogModule.log("获取提现金额选项失败: " + e.message, "ERROR");
                if (callback) {
                    callback(e, null);
                }
            }
        }
    };
})();

// 验证模块 - 处理各种数据验证
const ValidationModule = (function () {
    return {
        /**
         * 验证手机号是否有效
         * @param {string} phone - 手机号
         * @returns {boolean} 是否有效
         */
        isValidPhoneNumber: function (phone) {
            // 验证是否为11位数字，并且以1开头
            if (!phone || typeof phone !== 'string') {
                return false;
            }

            const phoneRegex = /^1\d{10}$/;
            return phoneRegex.test(phone);
        },

        /**
         * 验证验证码是否有效
         * @param {string} code - 验证码
         * @returns {boolean} 是否有效
         */
        isValidVerificationCode: function (code) {
            // 验证是否为6位数字
            if (!code || typeof code !== 'string') {
                return false;
            }

            const codeRegex = /^\d{6}$/;
            return codeRegex.test(code);
        },

        /**
         * 验证密码是否有效
         * @param {string} password - 密码
         * @returns {boolean} 是否有效
         */
        isValidPassword: function (password) {
            // 验证密码长度是否在6-20位之间
            if (!password || typeof password !== 'string') {
                return false;
            }

            return password.length >= 6 && password.length <= 20;
        },


    };
})();

// UI模块 - 处理所有界面相关操作
const UIModule = (function () {
    // UI组件引用
    let mainUI = null;
    let currentPage = "main"; // 当前页面：main, login, register, resetPassword, scriptCenter, dailyTask, agentSales, points, moonBox

    return {
        /**
         * 初始化UI
         */
        init: function () {
            LogModule.log("UI模块初始化");
            this.createMainUI();
        },

        /**
         * 获取当前页面
         * @returns {string} 当前页面标识
         */
        getCurrentPage: function () {
            return currentPage;
        },

        /**
         * 设置当前页面
         * @param {string} page - 页面标识
         */
        setCurrentPage: function (page) {
            currentPage = page;
            console.log("当前页面已设置为: " + page);
        },

        /**
         * 创建主界面
         */
        createMainUI: function () {
            try {
                // 创建UI布局
                var layoutXml =
                    '<frame>' +
                    '<vertical padding="16">' +
                    '<text id="appTitle" text="脚本助手" textSize="24sp" textColor="#3F51B5" gravity="center" margin="0 10"/>' +

                    '<card margin="10" cardCornerRadius="8dp" cardElevation="5dp">' +
                    '<img id="bannerImage" src="@drawable/ic_launcher" scaleType="centerCrop" h="150"/>' +
                    '</card>' +

                    '<card margin="10" cardCornerRadius="8dp" cardElevation="3dp">' +
                    '<vertical padding="16">' +
                    '<horizontal gravity="center_vertical">' +
                    '<text text="无障碍" textSize="16sp" layout_weight="1"/>' +
                    '<text text="(点击、滑动、长按等)" textSize="12sp" textColor="#888888" layout_weight="2"/>' +
                    '<Switch id="accessibilitySwitch" checked="false"/>' +
                    '</horizontal>' +

                    '<horizontal gravity="center_vertical" marginTop="20">' +
                    '<text text="悬浮框" textSize="16sp" layout_weight="1"/>' +
                    '<text text="(增加脚本存活率)" textSize="12sp" textColor="#888888" layout_weight="2"/>' +
                    '<Switch id="floatingWindowSwitch" checked="false"/>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</card>' +

                    '<horizontal margin="10 20">' +
                    '<button id="loginBtn" text="登录" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                    '<button id="registerBtn" text="注册" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</frame>';

                // 设置UI布局
                ui.layout(layoutXml);

                // 绑定事件
                this.bindEvents();

                // 初始化状态
                this.updateUIState();

                // 更新当前页面状态
                this.setCurrentPage("main");
                LogModule.log("主界面创建成功");
            } catch (e) {
                LogModule.log("创建主界面失败: " + e.message, "ERROR");
                console.error("创建主界面失败: " + e.message);
                console.error(e.stack);
            }
        },

        /**
         * 创建登录界面
         */
        createLoginUI: function () {
            try {
                // 创建登录UI布局
                var loginLayoutXml =
                    '<frame bg="#e8f5e9">' +
                    '<vertical padding="16" gravity="center_horizontal">' +
                    '<text id="loginTitle" text="登录" textSize="24sp" textColor="#333333" gravity="center" margin="0 20 0 40"/>' +

                    '<text text="手机号" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                    '<input id="phoneInput" hint="请输入手机号" inputType="phone" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                    '<text text="密码" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                    '<horizontal>' +
                    '<input id="passwordInput" hint="请输入密码" inputType="textPassword" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50" layout_weight="1"/>' +
                    '<text id="forgotPassword" text="忘记密码" textSize="14sp" textColor="#FF5722" gravity="center" marginLeft="10"/>' +
                    '</horizontal>' +

                    '<button id="loginSubmitBtn" text="登录" textSize="16sp" color="#ffffff" bg="#CD853F" marginTop="30" h="50"/>' +

                    '<horizontal marginTop="20" gravity="center">' +
                    '<text text="没有账号? " textSize="14sp" textColor="#666666"/>' +
                    '<text id="goToRegister" text="立即注册" textSize="14sp" textColor="#4CAF50" />' +
                    '</horizontal>' +

                    '<button id="backToMainBtn" text="返回" textSize="14sp" style="Widget.AppCompat.Button.Borderless" marginTop="20"/>' +
                    '</vertical>' +
                    '</frame>';

                // 设置UI布局
                ui.layout(loginLayoutXml);

                // 绑定登录页面事件
                this.bindLoginEvents();

                // 更新当前页面状态
                this.setCurrentPage("login");
                LogModule.log("登录界面创建成功");
            } catch (e) {
                LogModule.log("创建登录界面失败: " + e.message, "ERROR");
                console.error("创建登录界面失败: " + e.message);
                console.error(e.stack);
            }
        },

        /**
         * 绑定登录页面事件
         */
        bindLoginEvents: function () {
            var that = this;

            // 登录按钮
            ui.loginSubmitBtn.on("click", function () {
                var phone = ui.phoneInput.text();
                var password = ui.passwordInput.text();

                console.log("登录提交按钮被点击");

                // 再次检查无障碍服务状态
                let accessibilityEnabled = PermissionModule.isAccessibilityServiceEnabled();
                console.log("登录时无障碍服务状态: " + (accessibilityEnabled ? "已启用" : "未启用"));

                // 必须开启无障碍服务才能登录
                if (!accessibilityEnabled) {
                    console.log("无障碍服务未启用，显示设置对话框");
                    PermissionModule.showAccessibilitySettingsDialog();
                    return;
                }

                // 验证手机号格式
                if (!ValidationModule.isValidPhoneNumber(phone)) {
                    that.showToast("请输入正确的手机号");
                    console.log("手机号格式不正确: " + phone);
                    return;
                }

                // 验证密码
                if (!ValidationModule.isValidPassword(password)) {
                    that.showToast("密码长度不能少于6位");
                    console.log("密码格式不正确");
                    return;
                }

                // 显示加载提示
                that.showToast("登录中...");
                console.log("开始登录请求: " + phone);

                // 调用登录接口
                NetworkModule.login(phone, password, function (error, userData) {
                    if (error) {
                        // 登录失败
                        that.showToast("登录失败: " + error.message);
                        LogModule.log("登录失败: " + error.message, "ERROR");
                        console.log("登录失败: " + error.message);
                        console.log("错误详情: " + JSON.stringify(error));
                    } else {
                        // 登录成功
                        that.showToast("登录成功");
                        LogModule.log("登录成功: 用户ID=" + userData.id);
                        console.log("登录成功: " + JSON.stringify(userData));

                        // 跳转到脚本中心页面，并显示公告
                        that.createScriptCenterUI(true);
                    }
                });
            });

            // 忘记密码
            ui.forgotPassword.on("click", function () {
                LogModule.log("跳转到重置密码页面");
                that.createResetPasswordUI();
            });

            // 去注册
            ui.goToRegister.on("click", function () {
                LogModule.log("跳转到注册页面");
                that.createRegisterUI();
            });

            // 返回主页
            ui.backToMainBtn.on("click", function () {
                LogModule.log("返回主页");
                that.createMainUI();
            });
        },

        /**
         * 创建重置密码界面
         */
        createResetPasswordUI: function () {
            try {
                // 创建重置密码UI布局
                var resetPasswordLayoutXml =
                    '<frame bg="#e8f5e9">' +
                    '<vertical padding="16" gravity="center_horizontal">' +
                    '<text id="resetTitle" text="重置密码" textSize="24sp" textColor="#333333" gravity="center" margin="0 20 0 40"/>' +

                    '<text text="手机号" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                    '<input id="resetPhoneInput" hint="请输入手机号" inputType="phone" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                    '<horizontal marginTop="10">' +
                    '<input id="resetCodeInput" hint="请输入验证码" inputType="number" textSize="16sp" padding="8" bg="#ffffff" h="50" layout_weight="1"/>' +
                    '<button id="getResetCodeBtn" text="获取验证码" textSize="14sp" style="Widget.AppCompat.Button.Colored" marginLeft="10"/>' +
                    '</horizontal>' +

                    '<text text="新密码" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                    '<input id="newPasswordInput" hint="请输入新密码" inputType="textPassword" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                    '<button id="resetSubmitBtn" text="立即重置" textSize="16sp" color="#ffffff" bg="#CD853F" marginTop="30" h="50"/>' +

                    '<button id="backToLoginBtn" text="返回登录" textSize="14sp" style="Widget.AppCompat.Button.Borderless" marginTop="20"/>' +
                    '</vertical>' +
                    '</frame>';

                // 设置UI布局
                ui.layout(resetPasswordLayoutXml);

                // 绑定重置密码页面事件
                this.bindResetPasswordEvents();

                // 更新当前页面状态
                this.setCurrentPage("resetPassword");
                LogModule.log("重置密码界面创建成功");
            } catch (e) {
                LogModule.log("创建重置密码界面失败: " + e.message, "ERROR");
                console.error("创建重置密码界面失败: " + e.message);
                console.error(e.stack);
            }
        },

        /**
         * 创建注册界面
         */
        createRegisterUI: function () {
            try {
                // 创建注册UI布局
                var registerLayoutXml =
                    '<frame bg="#e8f5e9">' +
                    '<vertical padding="16" gravity="center_horizontal">' +
                    '<text id="registerTitle" text="注册" textSize="24sp" textColor="#333333" gravity="center" margin="0 20 0 40"/>' +

                    '<text text="手机号" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                    '<input id="registerPhoneInput" hint="请输入手机号" inputType="phone" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                    '<horizontal marginTop="10">' +
                    '<input id="registerCodeInput" hint="请输入验证码" inputType="number" textSize="16sp" padding="8" bg="#ffffff" h="50" layout_weight="1"/>' +
                    '<button id="getRegisterCodeBtn" text="获取验证码" textSize="14sp" style="Widget.AppCompat.Button.Colored" marginLeft="10"/>' +
                    '</horizontal>' +

                    '<text text="密码" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                    '<input id="registerPasswordInput" hint="请输入密码" inputType="textPassword" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                    '<text text="推荐人ID" textSize="16sp" textColor="#666666" marginTop="20"/>' +
                    '<input id="referrerIdInput" hint="请输入推荐人ID" textSize="16sp" marginTop="4" padding="8" bg="#ffffff" h="50"/>' +

                    '<button id="registerSubmitBtn" text="注册" textSize="16sp" color="#ffffff" bg="#CD853F" marginTop="30" h="50"/>' +

                    '<button id="backToLoginFromRegisterBtn" text="返回登录" textSize="14sp" style="Widget.AppCompat.Button.Borderless" marginTop="20"/>' +
                    '</vertical>' +
                    '</frame>';

                // 设置UI布局
                ui.layout(registerLayoutXml);

                // 绑定注册页面事件
                this.bindRegisterEvents();

                // 更新当前页面状态
                this.setCurrentPage("register");
                LogModule.log("注册界面创建成功");
            } catch (e) {
                LogModule.log("创建注册界面失败: " + e.message, "ERROR");
                console.error("创建注册界面失败: " + e.message);
                console.error(e.stack);
            }
        },

        /**
         * 绑定重置密码页面事件
         */
        bindResetPasswordEvents: function () {
            var that = this;

            // 获取验证码按钮
            ui.getResetCodeBtn.on("click", function () {
                var phone = ui.resetPhoneInput.text();

                // 验证手机号格式
                if (!ValidationModule.isValidPhoneNumber(phone)) {
                    that.showToast("请输入正确的手机号");
                    return;
                }

                // 禁用按钮并开始倒计时
                that.startCodeButtonCountdown(ui.getResetCodeBtn);

                // 调用发送重置密码验证码接口
                NetworkModule.sendResetPasswordCode(phone, function (error, result) {
                    if (error) {
                        that.showToast("获取验证码失败: " + error.message);
                        LogModule.log("获取重置密码验证码失败: " + error.message, "ERROR");
                    } else {
                        if (result && result.code === 200) {
                            that.showToast("验证码已发送，请注意查收");
                            LogModule.log("重置密码验证码已发送: " + phone);
                        } else {
                            let errorMsg = result && result.message ? result.message : "获取验证码失败";
                            that.showToast("获取验证码失败: " + errorMsg);
                            LogModule.log("获取重置密码验证码失败: " + errorMsg, "ERROR");
                        }
                    }
                });
            });

            // 重置密码按钮
            ui.resetSubmitBtn.on("click", function () {
                var phone = ui.resetPhoneInput.text();
                var code = ui.resetCodeInput.text();
                var newPassword = ui.newPasswordInput.text();

                // 验证手机号格式
                if (!ValidationModule.isValidPhoneNumber(phone)) {
                    that.showToast("请输入正确的手机号");
                    return;
                }

                // 验证验证码
                if (!code || code.length !== 6) {
                    that.showToast("请输入6位验证码");
                    return;
                }

                // 验证密码
                if (!ValidationModule.isValidPassword(newPassword)) {
                    that.showToast("密码长度不能少于6位");
                    return;
                }

                // 显示加载提示
                that.showToast("正在重置密码...");

                // 调用重置密码接口
                NetworkModule.resetPassword(phone, code, newPassword, function (error, result) {
                    if (error) {
                        that.showToast("重置密码失败: " + error.message);
                        LogModule.log("重置密码失败: " + error.message, "ERROR");
                    } else {
                        if (result && result.code === 200) {
                            that.showToast("密码重置成功，请重新登录");
                            LogModule.log("密码重置成功: " + phone);

                            // 返回登录页面
                            setTimeout(function () {
                                that.createLoginUI();
                            }, 1500);
                        } else {
                            let errorMsg = result && result.message ? result.message : "重置密码失败";
                            that.showToast("重置密码失败: " + errorMsg);
                            LogModule.log("重置密码失败: " + errorMsg, "ERROR");
                        }
                    }
                });
            });

            // 返回登录按钮
            ui.backToLoginBtn.on("click", function () {
                LogModule.log("返回登录页面");
                that.createLoginUI();
            });
        },

        /**
         * 绑定注册页面事件
         */
        bindRegisterEvents: function () {
            var that = this;

            // 获取验证码按钮
            ui.getRegisterCodeBtn.on("click", function () {
                var phone = ui.registerPhoneInput.text();

                // 验证手机号格式
                if (!ValidationModule.isValidPhoneNumber(phone)) {
                    that.showToast("请输入正确的手机号");
                    return;
                }

                // 检查手机号是否已注册
                NetworkModule.checkPhoneRegistered(phone, function (error, result) {
                    if (error) {
                        that.showToast("验证手机号失败: " + error.message);
                        LogModule.log("验证手机号失败: " + error.message, "ERROR");
                    } else if (result && result.code === 200) {
                        if (result.data === true) {
                            that.showToast("该手机号已注册，请直接登录");
                            LogModule.log("手机号已注册: " + phone);
                        } else {
                            // 禁用按钮并开始倒计时
                            that.startCodeButtonCountdown(ui.getRegisterCodeBtn);

                            // 调用发送注册验证码接口
                            NetworkModule.sendRegisterCode(phone, function (error, result) {
                                if (error) {
                                    that.showToast("获取验证码失败: " + error.message);
                                    LogModule.log("获取注册验证码失败: " + error.message, "ERROR");
                                } else {
                                    if (result && result.code === 200) {
                                        that.showToast("验证码已发送，请注意查收");
                                        LogModule.log("注册验证码已发送: " + phone);
                                    } else {
                                        let errorMsg = result && result.message ? result.message : "获取验证码失败";
                                        that.showToast("获取验证码失败: " + errorMsg);
                                        LogModule.log("获取注册验证码失败: " + errorMsg, "ERROR");
                                    }
                                }
                            });
                        }
                    } else {
                        let errorMsg = result && result.message ? result.message : "验证手机号失败";
                        that.showToast("验证手机号失败: " + errorMsg);
                        LogModule.log("验证手机号失败: " + errorMsg, "ERROR");
                    }
                });
            });

            // 注册按钮
            ui.registerSubmitBtn.on("click", function () {
                var phone = ui.registerPhoneInput.text();
                var code = ui.registerCodeInput.text();
                var password = ui.registerPasswordInput.text();
                var referrerId = ui.referrerIdInput.text();

                // 验证手机号格式
                if (!ValidationModule.isValidPhoneNumber(phone)) {
                    that.showToast("请输入正确的手机号");
                    return;
                }

                // 验证验证码
                if (!code || code.length !== 6) {
                    that.showToast("请输入6位验证码");
                    return;
                }

                // 验证密码
                if (!ValidationModule.isValidPassword(password)) {
                    that.showToast("密码长度不能少于6位");
                    return;
                }

                // 验证推荐人ID
                if (!referrerId) {
                    that.showToast("请输入推荐人ID");
                    return;
                }

                // 验证推荐人ID是否存在
                NetworkModule.checkReferrerId(referrerId, function (error, result) {
                    if (error) {
                        that.showToast("验证推荐人ID失败: " + error.message);
                        LogModule.log("验证推荐人ID失败: " + error.message, "ERROR");
                    } else if (result && result.code === 200 && result.data === true) {
                        // 显示加载提示
                        that.showToast("正在注册...");

                        // 获取设备ID
                        let deviceId = device.getAndroidId();

                        // 调用注册接口
                        NetworkModule.register(phone, code, password, referrerId, function (error, result) {
                            if (error) {
                                that.showToast("注册失败: " + error.message);
                                LogModule.log("注册失败: " + error.message, "ERROR");
                            } else {
                                // 检查响应状态码
                                if (result && result.code === 200) {
                                    that.showToast("注册成功，请登录");
                                    LogModule.log("注册成功: " + phone);

                                    // 返回登录页面
                                    setTimeout(function () {
                                        that.createLoginUI();
                                    }, 1500);
                                } else {
                                    // 处理业务逻辑错误
                                    let errorMsg = result && result.message ? result.message : "注册失败";
                                    that.showToast("注册失败: " + errorMsg);
                                    LogModule.log("注册失败: " + errorMsg, "ERROR");
                                }
                            }
                        });
                    } else {
                        let errorMsg = result && result.message ? result.message : "推荐人ID不存在";
                        that.showToast("推荐人ID不存在，请重新输入");
                        LogModule.log("推荐人ID不存在: " + referrerId + ", " + errorMsg, "ERROR");
                    }
                });
            });

            // 返回登录按钮
            ui.backToLoginFromRegisterBtn.on("click", function () {
                LogModule.log("返回登录页面");
                that.createLoginUI();
            });
        },

        /**
         * 开始验证码按钮倒计时
         * @param {Object} button - 按钮对象
         * @param {number} seconds - 倒计时秒数，默认60秒
         */
        startCodeButtonCountdown: function (button, seconds) {
            seconds = seconds || 60;
            var originalText = button.getText();
            button.setEnabled(false);

            var countdownInterval = setInterval(function () {
                seconds--;
                if (seconds <= 0) {
                    clearInterval(countdownInterval);
                    ui.run(function () {
                        button.setText(originalText);
                        button.setEnabled(true);
                    });
                } else {
                    ui.run(function () {
                        button.setText(seconds + "秒后重试");
                    });
                }
            }, 1000);
        },

        /**
         * 更新UI状态
         */
        updateUIState: function () {
            try {
                // 更新开关状态
                ui.accessibilitySwitch.checked = PermissionModule.checkAccessibilityPermission();
                ui.floatingWindowSwitch.checked = PermissionModule.checkFloatingWindowPermission();

                // 检查登录状态
                let isLoggedIn = ConfigModule.get("isLoggedIn");
                let userPhone = ConfigModule.get("userPhone");

                if (isLoggedIn && userPhone) {
                    // 如果已登录，更新UI显示
                    if (ui.loginBtn) {
                        ui.loginBtn.setText("已登录: " + this.maskPhone(userPhone));
                    }
                }

                // 尝试设置图片（如果有的话）
                try {
                    let path = "./res/banner.jpg"; // 尝试从应用资源加载图片
                    if (files.exists(path)) {
                        ui.bannerImage.setSource(path);
                        LogModule.log("已设置自定义横幅图片");
                    }
                } catch (e) {
                    LogModule.log("设置横幅图片失败: " + e.message);
                }
            } catch (e) {
                LogModule.log("更新UI状态失败: " + e.message, "ERROR");
            }
        },

        /**
         * 手机号脱敏处理
         * @param {string} phone - 手机号
         * @returns {string} 脱敏后的手机号
         */
        maskPhone: function (phone) {
            if (!phone || phone.length < 11) {
                return phone;
            }
            return phone.substring(0, 3) + "****" + phone.substring(7);
        },

        /**
         * 显示Toast消息
         * @param {string} message - 要显示的消息
         */
        showToast: function (message) {
            toast(message);
        },

        /**
         * 绑定主界面事件
         */
        bindEvents: function () {
            var that = this;

            // 无障碍服务开关
            ui.accessibilitySwitch.on("check", function (checked) {
                if (checked && !PermissionModule.checkAccessibilityPermission()) {
                    // 请求无障碍服务权限
                    PermissionModule.requestAccessibilityPermission();
                }
                ConfigModule.set("enableAccessibility", checked);
            });

            // 悬浮窗开关
            ui.floatingWindowSwitch.on("check", function (checked) {
                if (checked && !PermissionModule.checkFloatingWindowPermission()) {
                    // 请求悬浮窗权限
                    PermissionModule.requestFloatingWindowPermission();
                }
                ConfigModule.set("enableFloatWindow", checked);
            });

            // 登录按钮
            ui.loginBtn.on("click", function () {
                // 检查必要权限
                console.log("登录按钮被点击，开始检查权限");

                // 检查无障碍服务状态
                let accessibilityEnabled = PermissionModule.isAccessibilityServiceEnabled();
                console.log("无障碍服务状态: " + (accessibilityEnabled ? "已启用" : "未启用"));

                // 检查悬浮窗权限
                let floatingWindowEnabled = PermissionModule.isFloatingWindowPermissionGranted();
                console.log("悬浮窗权限状态: " + (floatingWindowEnabled ? "已授权" : "未授权"));

                // 必须开启无障碍服务才能登录
                if (!accessibilityEnabled) {
                    console.log("无障碍服务未启用，显示设置对话框");
                    PermissionModule.showAccessibilitySettingsDialog();
                    return;
                }

                // 悬浮窗权限可选，但给出提示
                if (!floatingWindowEnabled) {
                    that.showToast("建议开启悬浮窗权限以获得完整功能");
                    console.log("悬浮窗权限未授权，但允许继续");
                }

                // 跳转到登录页面
                console.log("准备跳转到登录页面");
                that.createLoginUI();
            });

            // 注册按钮
            ui.registerBtn.on("click", function () {
                // 检查必要权限
                if (!PermissionModule.checkAllRequiredPermissions()) {
                    that.showToast("请先开启无障碍服务和悬浮窗权限");
                    return;
                }

                // 跳转到注册页面
                that.createRegisterUI();
            });
        },

        /**
         * 创建脚本中心界面
         * @param {boolean} showAnnouncement - 是否显示公告，默认为false
         */
        createScriptCenterUI: function (showAnnouncement) {
            try {
                // 创建脚本中心UI布局
                var scriptCenterLayoutXml =
                    '<frame>' +
                    '<vertical bg="#F5F5F5" padding="8">' + // 浅灰色背景，增加内边距
                    '<frame bg="#F5F5F5" h="50">' +
                    '<horizontal gravity="center_vertical" h="*" w="*">' +
                    '<frame w="60dp" visibility="invisible"/>' + // 左侧占位符
                    '<text text="脚本助手" textSize="18sp" textColor="#333333" gravity="center" layout_weight="1" textStyle="bold"/>' +
                    '<frame w="60dp" visibility="invisible"/>' + // 右侧占位符
                    '</horizontal>' +
                    '</frame>' +

                    '<frame id="scriptTabContent" layout_weight="1">' +
                    '<scroll>' +
                    '<vertical>' +
                    // VIP脚本专区标题
                    '<card cardCornerRadius="8dp" cardElevation="2dp" margin="5 8 5 5">' +
                    '<text text="VIP脚本专区" textSize="16sp" textColor="#FFFFFF" gravity="center" bg="#009688" h="40" w="*"/>' +
                    '</card>' +

                    // VIP脚本专区 - 第1排
                    '<horizontal margin="0 5" gravity="center" w="*">' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="moon_box">' +
                    '<text id="moon_box_text" text="月光宝盒" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="vip_wx">' +
                    '<text id="vip_wx_text" text="微信" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '</horizontal>' +

                    // VIP脚本专区 - 第2排
                    '<horizontal margin="0 5" gravity="center" w="*">' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="vip_qq">' +
                    '<text id="vip_qq_text" text="QQ" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="vip_wb">' +
                    '<text id="vip_wb_text" text="微博" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '</horizontal>' +

                    // VIP脚本专区 - 第3排
                    '<horizontal margin="0 5" gravity="center" w="*">' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="vip_script1">' +
                    '<text id="vip_script1_text" text="VIP脚本1" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="vip_script2">' +
                    '<text id="vip_script2_text" text="VIP脚本2" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '</horizontal>' +

                    // VIP脚本专区 - 第4排
                    '<horizontal margin="0 5" gravity="center" w="*">' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="vip_script3">' +
                    '<text id="vip_script3_text" text="VIP脚本3" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="vip_script4">' +
                    '<text id="vip_script4_text" text="VIP脚本4" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '</horizontal>' +

                    // 免费脚本专区标题
                    '<card cardCornerRadius="8dp" cardElevation="2dp" margin="5 12 5 5">' +
                    '<text text="免费脚本" textSize="16sp" textColor="#FFFFFF" gravity="center" bg="#009688" h="40" w="*"/>' +
                    '</card>' +

                    // 免费脚本专区 - 第1排
                    '<horizontal margin="0 5" gravity="center" w="*">' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_wx_read">' +
                    '<text id="free_wx_read" text="微信阅读" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_wx">' +
                    '<text id="free_wx_text" text="微信" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '</horizontal>' +

                    // 免费脚本专区 - 第2排
                    '<horizontal margin="0 5" gravity="center" w="*">' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_qq">' +
                    '<text id="free_qq_text" text="QQ" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_wb">' +
                    '<text id="free_wb_text" text="微博" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '</horizontal>' +

                    // 免费脚本专区 - 第3排
                    '<horizontal margin="0 5" gravity="center" w="*">' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_dy">' +
                    '<text id="free_dy_text" text="抖音" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_ks">' +
                    '<text id="free_ks_text" text="快手" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '</horizontal>' +

                    // 免费脚本专区 - 第4排
                    '<horizontal margin="0 5" gravity="center" w="*">' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_tb">' +
                    '<text id="free_tb_text" text="淘宝" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_jd">' +
                    '<text id="free_jd_text" text="京东" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '</horizontal>' +

                    // 免费脚本专区 - 第5排
                    '<horizontal margin="0 5" gravity="center" w="*">' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_blbl">' +
                    '<text id="free_blbl_text" text="哔哩哔哩" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '<card w="*" h="50" cardCornerRadius="8dp" cardElevation="2dp" layout_weight="1" margin="5 0">' +
                    '<vertical gravity="center" bg="#009688" h="*" w="*" clickable="true" id="free_toutiao">' +
                    '<text id="free_toutiao_text" text="今日头条" textSize="16sp" textColor="#FFFFFF" gravity="center"/>' +
                    '</vertical>' +
                    '</card>' +
                    '</horizontal>' +

                    // 底部留白
                    '<text text="" margin="0 10"/>' +
                    '</vertical>' +
                    '</scroll>' +
                    '</frame>' +

                    '<frame id="myTabContent" layout_weight="1" visibility="gone">' +
                    '<scroll>' +
                    '<vertical padding="10" bg="#f5ffe0">' +
                    // 个人中心卡片
                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5 5 10">' +
                    '<vertical padding="15">' +
                    '<horizontal>' +
                    '<img src="@android:drawable/ic_menu_myplaces" w="60" h="60" margin="0 0 10 0"/>' +
                    '<vertical layout_weight="1">' +
                    '<text id="accountInfoText" text="我的账户: xxxx" textSize="14sp" textColor="#333333"/>' +
                    '<text id="referrerInfoText" text="上级: xxxx" textSize="14sp" textColor="#333333" marginTop="3"/>' +
                    '<text id="deviceIdText" text="设备码:" textSize="14sp" textColor="#333333" marginTop="3"/>' +
                    '<text id="invitationCodeText" text="邀请码:" textSize="14sp" textColor="#333333" marginTop="3"/>' +
                    '</vertical>' +
                    '</horizontal>' +
                    '<horizontal gravity="right" marginTop="5">' +
                    '<text text="卡密有效期: " textSize="13sp" textColor="#666666"/>' +
                    '<text id="expiryDateText" text="2025.7.1" textSize="13sp" textColor="#FF5722" marginLeft="5"/>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</card>' +

                    // 卡密区域
                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                    '<vertical padding="15">' +
                    '<horizontal gravity="center_vertical">' +
                    '<text text="卡密" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                    '<input id="cardKeyInput" hint="兑换卡密" textSize="14sp" padding="8dp" bg="#f5f5f5" layout_weight="3"/>' +
                    '<button id="exchangeBtn" text="兑换" textSize="14sp" w="60dp" h="40dp" style="Widget.AppCompat.Button.Colored" margin="5 0 0 0"/>' +
                    '</horizontal>' +
                    '<horizontal gravity="center" marginTop="10">' +
                    '<button id="buyCardBtn" text="购买卡密" textSize="14sp" w="80dp" h="40dp" margin="5"/>' +
                    '<button id="bindDeviceBtn" text="绑定设备" textSize="14sp" w="80dp" h="40dp" margin="5"/>' +
                    '<button id="renewCardBtn" text="解绑设备" textSize="14sp" w="80dp" h="40dp" margin="5"/>' +
                    '</horizontal>' +
                    '<text text="注: 解绑设备需扣除3600分钟" textSize="13sp" textColor="#FF5722" marginTop="5"/>' +
                    '</vertical>' +
                    '</card>' +

                    // 功能菜单列表 - 修改每日任务菜单项，添加id="dailyTaskBtn"
                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="dailyTaskCard">' +
                    '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="dailyTaskBtn">' +
                    '<img src="@android:drawable/ic_menu_today" w="24" h="24" tint="#333333" marginRight="10"/>' +
                    '<text text="每日任务" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                    '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                    '</horizontal>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="agentSalesCard">' +
                    '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="agentSalesBtn">' +
                    '<img src="@android:drawable/ic_menu_compass" w="24" h="24" tint="#333333" marginRight="10"/>' +
                    '<text text="代理分销" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                    '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                    '</horizontal>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="myPointsCard">' +
                    '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="myPointsBtn">' +
                    '<img src="@android:drawable/btn_star_big_on" w="24" h="24" tint="#333333" marginRight="10"/>' +
                    '<text text="我的积分" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                    '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                    '</horizontal>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="pointsExchangeCard">' +
                    '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="pointsExchangeBtn">' +
                    '<img src="@android:drawable/ic_menu_rotate" w="24" h="24" tint="#333333" marginRight="10"/>' +
                    '<text text="积分兑换" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                    '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                    '</horizontal>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="accountWithdrawCard">' +
                    '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="accountWithdrawBtn">' +
                    '<img src="@android:drawable/ic_menu_agenda" w="24" h="24" tint="#333333" marginRight="10"/>' +
                    '<text text="账户提现" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                    '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                    '</horizontal>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="promotionCard">' +
                    '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="promotionRankBtn">' +
                    '<img src="@android:drawable/ic_menu_share" w="24" h="24" tint="#333333" marginRight="10"/>' +
                    '<text text="推广排名" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                    '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                    '</horizontal>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="aboutUsCard">' +
                    '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="aboutUsBtn">' +
                    '<img src="@android:drawable/ic_menu_info_details" w="24" h="24" tint="#333333" marginRight="10"/>' +
                    '<text text="关于我们" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                    '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                    '</horizontal>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="moreProjectsCard">' +
                    '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="moreProjectsBtn">' +
                    '<img src="@android:drawable/ic_menu_more" w="24" h="24" tint="#333333" marginRight="10"/>' +
                    '<text text="更多项目" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                    '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                    '</horizontal>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="wealthExchangeCard">' +
                    '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="wealthExchangeBtn">' +
                    '<img src="@android:drawable/ic_menu_send" w="24" h="24" tint="#333333" marginRight="10"/>' +
                    '<text text="发财交流群" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                    '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                    '</horizontal>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5" id="sleepNotificationCard">' +
                    '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="sleepNotificationBtn">' +
                    '<img src="@android:drawable/ic_popup_reminder" w="24" h="24" tint="#333333" marginRight="10"/>' +
                    '<text text="脚本通知群" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                    '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                    '</horizontal>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 5 5 20" id="userManualCard">' +
                    '<horizontal gravity="center_vertical" padding="15" bg="#ffffff" clickable="true" id="userManualBtn">' +
                    '<img src="@android:drawable/ic_menu_help" w="24" h="24" tint="#333333" marginRight="10"/>' +
                    '<text text="使用教程" textSize="16sp" textColor="#333333" layout_weight="1"/>' +
                    '<img src="@android:drawable/ic_media_play" w="20" h="20" tint="#999999"/>' +
                    '</horizontal>' +
                    '</card>' +
                    '</vertical>' +
                    '</scroll>' +
                    '</frame>' +

                    '<card cardCornerRadius="0dp" cardElevation="5dp">' +
                    '<horizontal bg="#FFFFFF" h="50" gravity="center_vertical">' +
                    '<vertical layout_weight="1" gravity="center" id="scriptTabBtn" clickable="true">' +
                    '<text id="scriptTabText" text="脚本" textSize="16sp" textColor="#009688" gravity="center"/>' +
                    '</vertical>' +
                    '<vertical layout_weight="1" gravity="center" id="myTabBtn" clickable="true">' +
                    '<text id="myTabText" text="我的" textSize="16sp" textColor="#9E9E9E" gravity="center"/>' +
                    '</vertical>' +
                    '</horizontal>' +
                    '</card>' +
                    '</vertical>' +
                    '</frame>';

                // 设置UI布局
                ui.layout(scriptCenterLayoutXml);

                // 绑定脚本中心页面事件
                this.bindScriptCenterEvents();

                // 绑定我的页面事件
                this.bindMyPageEvents();

                // 更新当前页面状态
                this.setCurrentPage("scriptCenter");
                LogModule.log("脚本中心界面创建成功");

                // 获取最新公告并显示（仅在showAnnouncement为true时显示）
                if (showAnnouncement === true) {
                    this.fetchAndShowAnnouncement();
                    // 设置标志，表示已经显示过公告
                    ConfigModule.set("hasShownAnnouncement", "true");
                }
            } catch (e) {
                LogModule.log("创建脚本中心界面失败: " + e.message, "ERROR");
                console.error("创建脚本中心界面失败: " + e.message);
                console.error(e.stack);
            }
        },

        /**
         * 绑定脚本中心页面事件
         */
        bindScriptCenterEvents: function () {
            try {
                // 设置标签切换逻辑
                var that = this;

                // 脚本标签按钮点击事件
                ui.scriptTabBtn.on("click", function () {
                    try {
                        ui.scriptTabContent.attr("visibility", "visible");
                        ui.myTabContent.attr("visibility", "gone");

                        // 更新标签样式 - 使用直接ID访问
                        if (ui.scriptTabText) {
                            ui.scriptTabText.attr("textColor", "#009688");
                        }
                        if (ui.myTabText) {
                            ui.myTabText.attr("textColor", "#9E9E9E");
                        }

                        return true; // 返回true阻止事件继续传播
                    } catch (e) {
                        LogModule.log("处理脚本标签点击事件失败: " + e.message, "ERROR");
                        return true;
                    }
                });

                // 我的标签按钮点击事件
                ui.myTabBtn.on("click", function () {
                    try {
                        ui.scriptTabContent.attr("visibility", "gone");
                        ui.myTabContent.attr("visibility", "visible");

                        // 更新标签样式 - 使用直接ID访问
                        if (ui.scriptTabText) {
                            ui.scriptTabText.attr("textColor", "#9E9E9E");
                        }
                        if (ui.myTabText) {
                            ui.myTabText.attr("textColor", "#009688");
                        }

                        return true; // 返回true阻止事件继续传播
                    } catch (e) {
                        LogModule.log("处理我的标签点击事件失败: " + e.message, "ERROR");
                        return true;
                    }
                });

                // 绑定脚本项点击事件
                this.bindScriptItemEvents();
            } catch (e) {
                LogModule.log("绑定脚本中心事件失败: " + e.message, "ERROR");
            }
        },

        /**
         * 绑定脚本项点击事件
         */
        bindScriptItemEvents: function () {
            try {
                var that = this;

                // VIP脚本项点击事件
                var vipScriptItems = ["moon_box", "vip_wx", "vip_qq", "vip_wb", "vip_script1", "vip_script2", "vip_script3", "vip_script4"];
                vipScriptItems.forEach(function (id) {
                    if (ui[id]) {
                        ui[id].on("click", function () {
                            try {
                                // 获取脚本名称 - 使用直接ID访问
                                var textId = id + "_text";
                                var scriptName = id.replace("vip_", "");

                                // 如果文本元素存在，则使用其文本内容
                                if (ui[textId]) {
                                    try {
                                        scriptName = ui[textId].text();
                                    } catch (e) {
                                        LogModule.log("无法获取文本内容: " + e.message);
                                    }
                                }

                                // that.showToast("您点击了VIP脚本: " + scriptName);
                                //LogModule.log("用户点击了VIP脚本: " + scriptName);

                                var expiryDateText = ui.findById("expiryDateText");
                                console.log(expiryDateText.getText())
                                // 根据脚本ID执行相应逻辑
                                // 验证卡密状态（异步验证）
                                UIModule.validateCardKey(function (isValid, message) {
                                    if (!isValid) {
                                        that.showToast(message || "没有卡密信息或卡密已过期，请续费后使用");
                                        return;
                                    }
                                    // 卡密验证通过，启动月光宝盒脚本
                                    console.log("卡密验证通过，启动月光宝盒脚本");
                                    if (id === "moon_box") {
                                        MoonBoxModule.createMoonBoxUI();
                                        return true; // 返回true阻止事件继续传播
                                    }else{
                                        toast("更多脚本正在开发中...")
                                    }

                                });


                            } catch (e) {
                                LogModule.log("处理VIP脚本点击事件失败: " + e.message, "ERROR");
                                return true;
                            }
                        });
                    }
                });



                // 免费脚本项点击事件
                var freeScriptItems = ["free_wx_read", "free_wx", "free_qq", "free_wb", "free_dy", "free_ks", "free_tb", "free_jd", "free_blbl", "free_toutiao"];
                freeScriptItems.forEach(function (id) {
                    if (ui[id]) {
                        ui[id].on("click", function () {
                            try {
                                // 获取脚本名称 - 使用直接ID访问
                                var textId = id + "_text";
                                var scriptName = id.replace("free_", "");

                                // 如果文本元素存在，则使用其文本内容
                                switch(id){
                                    case "free_wx_read":
                                        that.createWxReadConfigPage();
                                        break;
                                    default:
                                        that.showToast("更多免费脚本正在开发中。。");
                                        break;
                                }

                                
                                LogModule.log("用户点击了免费脚本: " + scriptName);

                                // 这里可以添加脚本执行逻辑
                                // ...

                                return true; // 返回true阻止事件继续传播
                            } catch (e) {
                                LogModule.log("处理免费脚本点击事件失败: " + e.message, "ERROR");
                                return true;
                            }
                        });
                    }
                });
            } catch (e) {
                LogModule.log("绑定脚本项点击事件失败: " + e.message, "ERROR");
            }
        },

        /**
         * 验证卡密是否过期
         * @param {function} callback - 回调函数，参数为 (isValid, message)
         */
        validateCardKey: function (callback) {
            try {
                var userPhone = ConfigModule.get("userPhone");
                if (!userPhone) {
                    LogModule.log("用户未登录，无法验证卡密");
                    if (callback) callback(false, "用户未登录，无法验证卡密");
                    return;
                }

                console.log("开始验证卡密状态，用户手机号: " + userPhone);

                // 调用API查询卡密信息
                NetworkModule.queryCardKeyInfo(userPhone, function (error, result) {
                    try {
                        if (error) {
                            console.error("查询卡密信息失败: " + error.message);
                            LogModule.log("查询卡密信息失败: " + error.message, "ERROR");
                            if (callback) callback(false, "查询卡密信息失败，请检查网络连接");
                            return;
                        }

                        console.log("卡密查询结果: " + JSON.stringify(result));

                        // 检查返回结果
                        if (!result || result.code !== 200) {
                            console.log("卡密查询返回错误: " + (result ? result.message : "未知错误"));
                            LogModule.log("卡密查询返回错误: " + (result ? result.message : "未知错误"), "ERROR");
                            if (callback) callback(false, result && result.message ? result.message : "查询卡密信息失败");
                            return;
                        }

                        // 检查卡密数据是否存在
                        if (!result.data || result.data === null || result.data === undefined) {
                            console.log("没有卡密信息");
                            LogModule.log("没有卡密信息", "INFO");
                            if (callback) callback(false, "您还没有购买卡密，请先购买卡密后使用");
                            return;
                        }

                        // 将后端时间字符串转换为Date对象
                        // 处理 "2025-09-06 06:30:36" 格式的日期字符串
                        let backendDate;
                        try {
                            // 方法1: 直接使用Date构造函数
                            backendDate = new Date(result.data);

                            // 如果解析失败，尝试替换空格为T（ISO格式）
                            if (isNaN(backendDate.getTime())) {
                                console.log("尝试ISO格式解析...");
                                const isoDateString = result.data.replace(' ', 'T');
                                backendDate = new Date(isoDateString);
                            }

                            // 如果还是失败，手动解析
                            if (isNaN(backendDate.getTime())) {
                                console.log("尝试手动解析日期...");
                                const dateTimeParts = result.data.split(' ');
                                if (dateTimeParts.length === 2) {
                                    const datePart = dateTimeParts[0]; // "2025-09-06"
                                    const timePart = dateTimeParts[1]; // "06:30:36"

                                    const dateComponents = datePart.split('-');
                                    const timeComponents = timePart.split(':');

                                    if (dateComponents.length === 3 && timeComponents.length === 3) {
                                        // 注意：月份需要减1，因为JavaScript的月份是0-11
                                        backendDate = new Date(
                                            parseInt(dateComponents[0]), // 年
                                            parseInt(dateComponents[1]) - 1, // 月（0-11）
                                            parseInt(dateComponents[2]), // 日
                                            parseInt(timeComponents[0]), // 小时
                                            parseInt(timeComponents[1]), // 分钟
                                            parseInt(timeComponents[2])  // 秒
                                        );
                                    }
                                }
                            }
                        } catch (parseError) {
                            console.error("日期解析异常: " + parseError.message);
                        }

                        const currentDate = new Date();


                        // 检查日期是否有效
                        if (!backendDate || isNaN(backendDate.getTime())) {
                            if (callback) callback(false, "卡密信息格式错误，请联系客服");
                            return;
                        }

                        // 计算时间差（毫秒）
                        const timeDiff = backendDate - currentDate;

                        // 如果小于当前时间则是过期
                        if (timeDiff < 0) {
                            if (callback) callback(false, "您的卡密已过期，请续费后使用");
                            return;
                        }

                        // 卡密有效
                        LogModule.log("卡密验证通过", "INFO");
                        if (callback) callback(true, "卡密验证通过");

                    } catch (parseError) {
                        LogModule.log("解析卡密验证结果失败: " + parseError.message, "ERROR");
                        if (callback) callback(false, "验证卡密时出现错误，请重试");
                    }
                });
            } catch (e) {
                LogModule.log("验证卡密是否过期失败: " + e.message, "ERROR");
                if (callback) callback(false, "验证卡密时出现错误，请重试");
            }
        },

        /**
         * 获取并显示最新公告
         */
        fetchAndShowAnnouncement: function () {
            var that = this;

            // 调用获取最新公告接口
            NetworkModule.get("/announcement/latest", null, function (error, result) {
                if (error) {
                    LogModule.log("获取公告失败: " + error.message, "ERROR");
                    return;
                }

                if (result && result.code === 200 && result.data) {
                    // 显示公告弹窗
                    that.showAnnouncementDialog(result.data);
                } else {
                    LogModule.log("没有可用的公告");
                }
            });
        },

        /**
         * 显示公告弹窗
         * @param {Object} announcement - 公告数据
         */
        showAnnouncementDialog: function (announcement) {
            try {
                // 安全处理公告数据
                var title = announcement.title || "系统公告";
                var content = announcement.content || "暂无公告内容";
                var qqGroup = announcement.qqGroup || "";

                // 创建公告弹窗
                dialogs.build({
                    title: "公告",
                    content: content,
                    contentColor: "#666666",
                    positive: "官方QQ群",
                    negative: "关闭",
                    cancelable: false
                })
                    .on("positive", () => {
                        // 点击QQ群按钮
                        if (qqGroup) {
                            try {
                                app.openUrl(qqGroup);
                                LogModule.log("打开QQ群链接: " + qqGroup);
                            } catch (e) {
                                LogModule.log("打开QQ群链接失败: " + e.message, "ERROR");
                                toast("打开QQ群链接失败");
                            }
                        } else {
                            toast("QQ群链接不可用");
                        }
                    })
                    .on("negative", () => {
                        LogModule.log("公告已关闭");
                    })
                    .show();

                LogModule.log("公告弹窗已显示: " + title);
            } catch (e) {
                LogModule.log("显示公告弹窗失败: " + e.message, "ERROR");
            }
        },
        /**
         * 绑定我的页面事件
         */
        bindMyPageEvents: function () {
            try {
                var that = this;

                // 获取当前用户信息
                var userPhone = ConfigModule.get("userPhone");
                var userId = ConfigModule.get("userId");
                var deviceId = device.getAndroidId();

                // 设置用户信息
                if (ui.cardKeyInput) {
                    // 设置用户信息
                    var accountInfoText = ui.findById("accountInfoText");
                    if (accountInfoText) {
                        accountInfoText.setText("我的账户: " + this.maskPhone(userPhone));
                    }

                    var referrerInfoText = ui.findById("referrerInfoText");
                    if (referrerInfoText) {
                        var referrerId = ConfigModule.get("referrerId") || "无";
                        referrerInfoText.setText("上级: " + referrerId);
                    }

                    var deviceIdText = ui.findById("deviceIdText");
                    if (deviceIdText) {
                        deviceIdText.setText("设备码: " + deviceId);
                    }

                    var invitationCodeText = ui.findById("invitationCodeText");
                    if (invitationCodeText) {
                        var invitationCode = ConfigModule.get("invitationCode") || "未设置";
                        invitationCodeText.setText("邀请码: " + invitationCode);
                    }

                    // 查询卡密信息
                    this.queryCardKeyInfo(userPhone);

                    // 兑换卡密按钮点击事件
                    ui.exchangeBtn.on("click", function () {
                        var cardKey = ui.cardKeyInput.text();
                        if (!cardKey) {
                            that.showToast("请输入卡密");
                            return;
                        }

                        // 调用兑换卡密接口
                        that.redeemCardKey(cardKey, userPhone);
                    });

                    // 购买卡密按钮点击事件
                    ui.buyCardBtn.on("click", function () {
                        try {
                            // 跳转到购买卡密网页
                            app.openUrl("https://www.baidu.com");
                            LogModule.log("跳转到购买卡密网页");
                        } catch (e) {
                            LogModule.log("跳转到购买卡密网页失败: " + e.message, "ERROR");
                            that.showToast("跳转到购买卡密网页失败");
                        }
                    });

                    // 解绑设备按钮点击事件
                    ui.renewCardBtn.on("click", function () {
                        // 弹出确认对话框
                        dialogs.confirm("解绑设备提示",
                            "解绑设备将扣除3600分钟的卡密时长，确定要解绑吗？",
                            function (confirmed) {
                                if (confirmed) {
                                    // 调用解绑设备接口
                                    that.unbindDevice(userPhone, deviceId);
                                }
                            }
                        );
                    });

                    // 绑定设备按钮点击事件
                    ui.bindDeviceBtn.on("click", function () {
                        // 弹出确认对话框
                        dialogs.confirm("绑定设备提示",
                            "确定要将当前设备与账号绑定吗？绑定后其他设备将无法使用该账号。",
                            function (confirmed) {
                                if (confirmed) {
                                    // 调用绑定设备接口
                                    that.bindDevice(userPhone, deviceId);
                                }
                            }
                        );
                    });
                }

                // 处理推广排名卡片点击事件
                if (ui.promotionCard) {
                    ui.promotionCard.on("click", function () {
                        try {
                            console.log("推广排名卡片被点击");
                            LogModule.log("推广排名卡片被点击", "INFO");

                            // 调用创建推广排名界面的方法
                            UIManager.createPromotionRankUI();
                            return true; // 阻止事件继续传播
                        } catch (e) {
                            console.error("处理推广排名卡片点击事件失败: " + e.message);
                            console.error(e.stack);
                            LogModule.log("处理推广排名卡片点击事件失败: " + e.message, "ERROR");
                            that.showToast("加载推广排名页面失败: " + e.message);
                            return true;
                        }
                    });
                    console.log("推广排名卡片点击事件已绑定");
                }

                // 处理推广排名按钮点击事件
                if (ui.promotionRankBtn) {
                    ui.promotionRankBtn.on("click", function () {
                        try {
                            console.log("推广排名按钮被点击");
                            LogModule.log("推广排名按钮被点击", "INFO");

                            // 调用创建推广排名界面的方法
                            UIManager.createPromotionRankUI();
                            return true; // 阻止事件继续传播
                        } catch (e) {
                            console.error("处理推广排名按钮点击事件失败: " + e.message);
                            console.error(e.stack);
                            LogModule.log("处理推广排名按钮点击事件失败: " + e.message, "ERROR");
                            that.showToast("加载推广排名页面失败: " + e.message);
                            return true;
                        }
                    });
                    console.log("推广排名按钮点击事件已绑定");
                }

                // 处理关于我们卡片点击事件
                if (ui.aboutUsCard) {
                    ui.aboutUsCard.on("click", function () {
                        try {
                            console.log("关于我们卡片被点击");
                            LogModule.log("关于我们卡片被点击", "INFO");

                            // 调用创建关于我们界面的方法
                            UIManager.createAboutUsUI();
                            return true; // 阻止事件继续传播
                        } catch (e) {
                            console.error("处理关于我们卡片点击事件失败: " + e.message);
                            console.error(e.stack);
                            LogModule.log("处理关于我们卡片点击事件失败: " + e.message, "ERROR");

                            // 最终降级处理：直接使用alert
                            try {
                                alert("关于我们", "应用信息:\n脚本助手 v1.0.0\n更新日期: 2023年10月1日\n\n开发团队:\n我们是一支专注于自动化脚本开发的团队，致力于为用户提供高效便捷的自动化解决方案。");
                            } catch (alertError) {
                                toast("显示关于我们信息失败");
                            }

                            return true;
                        }
                    });
                    console.log("关于我们卡片点击事件已绑定");
                }

                // 处理关于我们按钮点击事件
                if (ui.aboutUsBtn) {
                    ui.aboutUsBtn.on("click", function () {
                        try {
                            console.log("关于我们按钮被点击");
                            LogModule.log("关于我们按钮被点击", "INFO");

                            // 调用创建关于我们界面的方法
                            UIManager.createAboutUsUI();
                            return true; // 阻止事件继续传播
                        } catch (e) {
                            console.error("处理关于我们按钮点击事件失败: " + e.message);
                            console.error(e.stack);
                            LogModule.log("处理关于我们按钮点击事件失败: " + e.message, "ERROR");

                            // 最终降级处理：直接使用alert
                            try {
                                alert("关于我们", "应用信息:\n脚本助手 v1.0.0\n更新日期: 2023年10月1日\n\n开发团队:\n我们是一支专注于自动化脚本开发的团队，致力于为用户提供高效便捷的自动化解决方案。");
                            } catch (alertError) {
                                toast("显示关于我们信息失败");
                            }

                            return true;
                        }
                    });
                    console.log("关于我们按钮点击事件已绑定");
                }

                // 绑定其他菜单项点击事件
                this.bindMyMenuEvents();

            } catch (e) {
                LogModule.log("绑定我的页面事件失败: " + e.message, "ERROR");
                console.error("绑定我的页面事件失败: " + e.message);
            }
        },

        /**
         * 绑定我的页面菜单事件
         */
        bindMyMenuEvents: function () {
            var that = this;

            // 单独处理账户提现卡片点击事件
            if (ui.accountWithdrawCard) {
                ui.accountWithdrawCard.on("click", function () {
                    try {
                        console.log("账户提现卡片被点击");
                        LogModule.log("账户提现卡片被点击", "INFO");

                        // 检查UIModule是否已定义
                        if (typeof UIModule === 'undefined') {
                            console.error("UIModule未定义，无法加载提现页面");
                            LogModule.log("UIModule未定义，无法加载提现页面", "ERROR");
                            that.showToast("加载提现页面失败: UIModule未定义");
                            return true;
                        }

                        // 跳转到提现页面
                        console.log("准备调用全局函数showWithdrawalPage() - 从卡片");
                        LogModule.log("准备调用全局函数showWithdrawalPage() - 从卡片", "INFO");

                        // 使用延迟执行，避免UI线程阻塞
                        setTimeout(function () {
                            try {
                                showWithdrawalPage();
                            } catch (delayedError) {
                                console.error("延迟执行showWithdrawalPage失败: " + delayedError.message);
                                console.error(delayedError.stack);
                                LogModule.log("延迟执行showWithdrawalPage失败: " + delayedError.message, "ERROR");
                                that.showToast("加载提现页面失败: " + delayedError.message);
                            }
                        }, 100);

                        return true; // 阻止事件继续传播
                    } catch (e) {
                        console.error("处理账户提现卡片点击事件失败: " + e.message);
                        console.error(e.stack); // 输出错误堆栈
                        LogModule.log("处理账户提现卡片点击事件失败: " + e.message, "ERROR");
                        that.showToast("加载提现页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("账户提现卡片点击事件已绑定");
            }

            // 单独处理账户提现按钮点击事件
            if (ui.accountWithdrawBtn) {
                ui.accountWithdrawBtn.on("click", function () {
                    try {
                        console.log("账户提现按钮被点击");
                        LogModule.log("账户提现按钮被点击", "INFO");

                        // 检查UIModule是否已定义
                        if (typeof UIModule === 'undefined') {
                            console.error("UIModule未定义，无法加载提现页面");
                            LogModule.log("UIModule未定义，无法加载提现页面", "ERROR");
                            that.showToast("加载提现页面失败: UIModule未定义");
                            return true;
                        }

                        // 跳转到提现页面
                        console.log("准备调用全局函数showWithdrawalPage()");
                        LogModule.log("准备调用全局函数showWithdrawalPage()", "INFO");

                        // 使用延迟执行，避免UI线程阻塞
                        setTimeout(function () {
                            try {
                                showWithdrawalPage();
                            } catch (delayedError) {
                                console.error("延迟执行showWithdrawalPage失败: " + delayedError.message);
                                console.error(delayedError.stack);
                                LogModule.log("延迟执行showWithdrawalPage失败: " + delayedError.message, "ERROR");
                                that.showToast("加载提现页面失败: " + delayedError.message);
                            }
                        }, 100);

                        return true; // 阻止事件继续传播
                    } catch (e) {
                        console.error("处理账户提现按钮点击事件失败: " + e.message);
                        console.error(e.stack); // 输出错误堆栈
                        LogModule.log("处理账户提现按钮点击事件失败: " + e.message, "ERROR");
                        that.showToast("加载提现页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("账户提现按钮点击事件已绑定");
            }

            // 单独处理我的积分卡片点击事件
            if (ui.myPointsCard) {
                ui.myPointsCard.on("click", function () {
                    try {
                        console.log("我的积分卡片被点击");
                        LogModule.log("我的积分卡片被点击", "INFO");
                        if (typeof pointsModule !== 'undefined' && pointsModule.showPointsPage) {
                            pointsModule.showPointsPage();
                        } else {
                            that.showToast("积分模块未加载");
                        }
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        console.error("处理积分卡片点击事件失败: " + e.message);
                        LogModule.log("处理积分卡片点击事件失败: " + e.message, "ERROR");
                        that.showToast("加载积分页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("我的积分卡片点击事件已绑定");
            }

            // 添加对myPointsBtn的点击事件处理
            if (ui.myPointsBtn) {
                ui.myPointsBtn.on("click", function () {
                    try {
                        console.log("我的积分按钮被点击");
                        LogModule.log("我的积分按钮被点击", "INFO");
                        if (typeof pointsModule !== 'undefined' && pointsModule.showPointsPage) {
                            pointsModule.showPointsPage();
                        } else {
                            that.showToast("积分模块未加载");
                        }
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        console.error("处理积分按钮点击事件失败: " + e.message);
                        LogModule.log("处理积分按钮点击事件失败: " + e.message, "ERROR");
                        that.showToast("加载积分页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("我的积分按钮点击事件已绑定");
            }

            // 处理每日任务按钮点击事件
            if (ui.dailyTaskBtn) {
                ui.dailyTaskBtn.on("click", function () {
                    try {
                        console.log("每日任务按钮被点击");
                        LogModule.log("每日任务按钮被点击", "INFO");
                        // 调用创建每日任务界面的方法
                        UIManager.createDailyTaskUI();
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        console.error("处理每日任务按钮点击事件失败: " + e.message);
                        LogModule.log("处理每日任务按钮点击事件失败: " + e.message, "ERROR");
                        that.showToast("加载每日任务页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("每日任务按钮点击事件已绑定");
            }

            // 处理代理分销按钮点击事件
            if (ui.agentSalesBtn) {
                ui.agentSalesBtn.on("click", function () {
                    try {
                        console.log("代理分销按钮被点击");
                        LogModule.log("代理分销按钮被点击", "INFO");
                        // 调用创建代理分销界面的方法
                        UIManager.createAgentSalesUI();
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        console.error("处理代理分销按钮点击事件失败: " + e.message);
                        LogModule.log("处理代理分销按钮点击事件失败: " + e.message, "ERROR");
                        that.showToast("加载代理分销页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("代理分销按钮点击事件已绑定");
            }

            // 处理每日任务卡片点击事件（整个卡片）
            if (ui.dailyTaskCard) {
                ui.dailyTaskCard.on("click", function () {
                    try {
                        console.log("每日任务卡片被点击");
                        LogModule.log("每日任务卡片被点击", "INFO");
                        // 调用创建每日任务界面的方法
                        UIManager.createDailyTaskUI();
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        console.error("处理每日任务卡片点击事件失败: " + e.message);
                        LogModule.log("处理每日任务卡片点击事件失败: " + e.message, "ERROR");
                        that.showToast("加载每日任务页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("每日任务卡片点击事件已绑定");
            }

            // 处理代理分销卡片点击事件（整个卡片）
            if (ui.agentSalesCard) {
                ui.agentSalesCard.on("click", function () {
                    try {
                        console.log("代理分销卡片被点击");
                        LogModule.log("代理分销卡片被点击", "INFO");
                        // 调用创建代理分销界面的方法
                        UIManager.createAgentSalesUI();
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        console.error("处理代理分销卡片点击事件失败: " + e.message);
                        LogModule.log("处理代理分销卡片点击事件失败: " + e.message, "ERROR");
                        that.showToast("加载代理分销页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("代理分销卡片点击事件已绑定");
            }

            // 处理积分兑换卡片点击事件（整个卡片）
            if (ui.pointsExchangeCard) {
                ui.pointsExchangeCard.on("click", function () {
                    try {
                        console.log("积分兑换卡片被点击");
                        LogModule.log("积分兑换卡片被点击", "INFO");
                        // 调用积分兑换页面
                        pointsExchangeModule.showPointsExchangePage();
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        console.error("处理积分兑换卡片点击事件失败: " + e.message);
                        LogModule.log("处理积分兑换卡片点击事件失败: " + e.message, "ERROR");
                        that.showToast("加载积分兑换页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("积分兑换卡片点击事件已绑定");
            }

            // 处理积分兑换按钮点击事件
            if (ui.pointsExchangeBtn) {
                ui.pointsExchangeBtn.on("click", function () {
                    try {
                        console.log("积分兑换按钮被点击");
                        LogModule.log("积分兑换按钮被点击", "INFO");
                        // 调用积分兑换页面
                        pointsExchangeModule.showPointsExchangePage();
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        console.error("处理积分兑换按钮点击事件失败: " + e.message);
                        LogModule.log("处理积分兑换按钮点击事件失败: " + e.message, "ERROR");
                        that.showToast("加载积分兑换页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("积分兑换按钮点击事件已绑定");
            }

            // 处理关于我们按钮点击事件
            if (ui.aboutUsBtn) {
                ui.aboutUsBtn.on("click", function () {
                    try {
                        console.log("关于我们按钮被点击");
                        LogModule.log("关于我们按钮被点击", "INFO");
                        // 调用创建关于我们界面的方法
                        UIManager.createAboutUsUI();
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        console.error("处理关于我们按钮点击事件失败: " + e.message);
                        LogModule.log("处理关于我们按钮点击事件失败: " + e.message, "ERROR");
                        that.showToast("加载关于我们页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("关于我们按钮点击事件已绑定");
            }

            // 处理更多项目按钮点击事件
            if (ui.moreProjectsBtn) {
                ui.moreProjectsBtn.on("click", function () {
                    try {
                        console.log("更多项目按钮被点击");
                        LogModule.log("更多项目按钮被点击", "INFO");
                        // 调用创建更多项目界面的方法
                        UIManager.createMoreProjectsUI();
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        console.error("处理更多项目按钮点击事件失败: " + e.message);
                        LogModule.log("处理更多项目按钮点击事件失败: " + e.message, "ERROR");
                        that.showToast("加载更多项目页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("更多项目按钮点击事件已绑定");
            }

            // 处理发财交流群按钮点击事件
            if (ui.wealthExchangeBtn) {
                ui.wealthExchangeBtn.on("click", function () {
                    try {
                        // 调用创建更多项目界面的方法 
                        UIManager.jumpWealthExchange();
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        that.showToast("加载MONEY_GROUP_LINK页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("更多MONEY_GROUP_LINK事件已绑定");
            }

            // 处理处理脚本按钮点击事件
            if (ui.wealthExchangeBtn) {
                ui.wealthExchangeBtn.on("click", function () {
                    try {
                        // 调用创建更多项目界面的方法 
                        UIManager.jumpWealthExchange();
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        that.showToast("加载MONEY_GROUP_LINK页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("更多MONEY_GROUP_LINK事件已绑定");
            }

            // 处理脚本通知群按钮点击事件
            if (ui.sleepNotificationBtn) {
                ui.sleepNotificationBtn.on("click", function () {
                    try {
                        // 调用创建更多项目界面的方法 
                        UIManager.jumpScriptNotice();
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        that.showToast("加载SCRIPT_NOTICE_GROUP_LINK页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("更多SCRIPT_NOTICE_GROUP_LINK事件已绑定");
            }

            // 处理使用教程按钮点击事件
            if (ui.userManualBtn) {
                ui.userManualBtn.on("click", function () {
                    try {
                        console.log("使用教程按钮被点击");
                        LogModule.log("使用教程按钮被点击", "INFO");
                        // 调用创建使用教程界面的方法
                        UIManager.createTutorialUI();
                        return true; // 阻止事件继续传播
                    } catch (e) {
                        console.error("处理使用教程按钮点击事件失败: " + e.message);
                        console.error(e.stack);
                        LogModule.log("处理使用教程按钮点击事件失败: " + e.message, "ERROR");
                        that.showToast("加载使用教程页面失败: " + e.message);
                        return true;
                    }
                });
                console.log("使用教程按钮点击事件已绑定");
            }
        },

        /**
         * 查询用户卡密信息
         * @param {string} phone - 用户手机号
         */
        queryCardKeyInfo: function (phone) {
            try {
                var that = this;


                // 调用API查询卡密信息
                NetworkModule.queryCardKeyInfo(phone, function (error, result) {
                    if (error) {
                        that.showToast("查询卡密信息失败: " + error.message);
                        LogModule.log("查询卡密信息失败: " + error.message, "ERROR");
                        return;
                    }

                    // 更新UI显示
                    ui.run(function () {
                        try {
                            // 设置有效期
                            var expiryDateText = ui.findById("expiryDateText");
                            if (expiryDateText) {
                                if (result && result.code === 200 && result.data) {

                                    // 改进日期解析方式
                                    var expiryDate;
                                    try {
                                        // 尝试直接解析
                                        expiryDate = new Date(result.data);

                                        // 检查日期是否有效
                                        if (isNaN(expiryDate.getTime())) {

                                            // 手动解析日期字符串 (格式: "2025-07-08 19:30:05")
                                            var parts = result.data.split(' ');
                                            var dateParts = parts[0].split('-');
                                            var timeParts = parts[1].split(':');

                                            expiryDate = new Date(
                                                parseInt(dateParts[0]), // 年
                                                parseInt(dateParts[1]) - 1, // 月 (0-11)
                                                parseInt(dateParts[2]), // 日
                                                parseInt(timeParts[0]), // 时
                                                parseInt(timeParts[1]), // 分
                                                parseInt(timeParts[2])  // 秒
                                            );
                                        }
                                    } catch (parseError) {

                                        // 尝试最简单的方式显示原始字符串
                                        expiryDateText.setText(result.data);
                                        expiryDateText.setTextColor(android.graphics.Color.parseColor("#FF5722"));
                                        return;
                                    }

                                    var currentDate = new Date();


                                    // 比较日期
                                    if (expiryDate > currentDate) {
                                        // 卡密有效
                                        expiryDateText.setText(result.data);
                                        expiryDateText.setTextColor(android.graphics.Color.parseColor("#FF5722")); // 橙色
                                    } else {
                                        // 卡密已过期
                                        expiryDateText.setText("已过期");
                                        expiryDateText.setTextColor(android.graphics.Color.parseColor("#F44336")); // 红色
                                    }
                                } else {
                                    // 没有卡密信息
                                    expiryDateText.setText("已过期");
                                    expiryDateText.setTextColor(android.graphics.Color.parseColor("#F44336")); // 红色
                                }
                            }

                            LogModule.log("卡密信息已更新");
                        } catch (e) {
                            console.error("更新卡密信息UI失败: " + e.message);
                            console.error(e.stack);
                        }
                    });
                });
            } catch (e) {
                LogModule.log("执行查询卡密信息操作失败: " + e.message, "ERROR");
                this.showToast("查询卡密信息出错");
            }
        },

        /**
         * 兑换卡密
         * @param {string} cardKey - 卡密
         * @param {string} phone - 用户手机号
         */
        redeemCardKey: function (cardKey, phone) {
            try {
                var that = this;
                var deviceId = device.getAndroidId();

                // 显示加载提示
                this.showToast("正在兑换卡密...");

                // 调用API兑换卡密
                NetworkModule.redeemCardKey(cardKey, phone, deviceId, function (error, result) {
                    if (error) {
                        that.showToast("兑换卡密失败: " + error.message);
                        LogModule.log("兑换卡密失败: " + error.message, "ERROR");
                        return;
                    }

                    if (result && result.code === 200) {
                        that.showToast("卡密兑换成功");
                        LogModule.log("卡密兑换成功");

                        // 如果返回了新的到期时间，直接更新UI
                        if (result.data) {
                            ui.run(function () {
                                try {
                                    var expiryDateText = ui.findById("expiryDateText");
                                    if (expiryDateText) {
                                        // 打印原始数据以便调试
                                        console.log("兑换后返回的原始数据: ", JSON.stringify(result));
                                        console.log("兑换后卡密到期时间字符串: " + result.data);

                                        // 改进日期解析方式
                                        var expiryDate;
                                        try {
                                            // 尝试直接解析
                                            expiryDate = new Date(result.data);

                                            // 检查日期是否有效
                                            if (isNaN(expiryDate.getTime())) {
                                                console.error("日期解析结果无效，尝试手动解析");

                                                // 手动解析日期字符串 (格式: "2025-07-08 19:30:05")
                                                var parts = result.data.split(' ');
                                                var dateParts = parts[0].split('-');
                                                var timeParts = parts[1].split(':');

                                                expiryDate = new Date(
                                                    parseInt(dateParts[0]), // 年
                                                    parseInt(dateParts[1]) - 1, // 月 (0-11)
                                                    parseInt(dateParts[2]), // 日
                                                    parseInt(timeParts[0]), // 时
                                                    parseInt(timeParts[1]), // 分
                                                    parseInt(timeParts[2])  // 秒
                                                );
                                            }
                                        } catch (parseError) {
                                            console.error("解析日期出错: " + parseError.message);
                                            LogModule.log("解析日期出错: " + parseError.message, "ERROR");

                                            // 尝试最简单的方式显示原始字符串
                                            expiryDateText.setText(result.data);
                                            expiryDateText.setTextColor(android.graphics.Color.parseColor("#FF5722"));
                                            return;
                                        }

                                        var currentDate = new Date();

                                        // 添加调试日志
                                        console.log("兑换后卡密到期时间对象: " + expiryDate);
                                        console.log("兑换后卡密到期时间格式化: " + expiryDate.toLocaleString());
                                        console.log("当前时间: " + currentDate.toLocaleString());
                                        console.log("卡密是否有效: " + (expiryDate > currentDate));
                                        LogModule.log("兑换后卡密到期时间: " + expiryDate.toLocaleString() + ", 当前时间: " + currentDate.toLocaleString(), "INFO");

                                        // 比较日期
                                        if (expiryDate > currentDate) {
                                            // 卡密有效
                                            expiryDateText.setText(result.data);
                                            expiryDateText.setTextColor(android.graphics.Color.parseColor("#FF5722")); // 橙色
                                        } else {
                                            // 卡密已过期
                                            expiryDateText.setText("已过期");
                                            expiryDateText.setTextColor(android.graphics.Color.parseColor("#F44336")); // 红色
                                        }
                                    }
                                } catch (e) {
                                    LogModule.log("更新兑换后卡密UI失败: " + e.message, "ERROR");
                                    console.error("更新兑换后卡密UI失败: " + e.message);
                                    console.error(e.stack);

                                    // 出错时尝试直接显示原始字符串
                                    try {
                                        if (expiryDateText && result.data) {
                                            expiryDateText.setText(result.data);
                                            expiryDateText.setTextColor(android.graphics.Color.parseColor("#FF5722"));
                                        }
                                    } catch (fallbackError) {
                                        console.error("回退显示也失败: " + fallbackError.message);
                                    }
                                }
                            });
                        } else {
                            // 如果没有返回新的到期时间，刷新卡密信息
                            that.queryCardKeyInfo(phone);
                        }
                    } else {
                        let errorMsg = result && result.message ? result.message : "兑换卡密失败";
                        that.showToast(errorMsg);
                        LogModule.log("兑换卡密响应错误: " + errorMsg, "ERROR");
                    }
                });
            } catch (e) {
                LogModule.log("执行兑换卡密操作失败: " + e.message, "ERROR");
                this.showToast("兑换卡密出错");
            }
        },

        /**
         * 解绑设备
         * @param {string} phone - 用户手机号
         * @param {string} deviceId - 设备ID
         */
        unbindDevice: function (phone, deviceId) {
            try {
                var that = this;

                // 显示加载提示
                this.showToast("正在解绑设备...");

                // 调用API解绑设备
                NetworkModule.unbindDevice(phone, deviceId, function (error, result) {
                    if (error) {
                        that.showToast("解绑设备失败: " + error.message);
                        LogModule.log("解绑设备失败: " + error.message, "ERROR");
                        return;
                    }

                    if (result && result.code === 200) {
                        that.showToast("设备解绑成功");

                        // 如果返回了新的到期时间，直接更新UI
                        if (result.data) {
                            ui.run(function () {
                                try {
                                    var expiryDateText = ui.findById("expiryDateText");
                                    if (expiryDateText) {
                                        // 改进日期解析方式
                                        var expiryDate;
                                        try {
                                            // 尝试直接解析
                                            expiryDate = new Date(result.data);

                                            // 检查日期是否有效
                                            if (isNaN(expiryDate.getTime())) {
                                                console.error("日期解析结果无效，尝试手动解析");

                                                // 手动解析日期字符串 (格式: "2025-07-08 19:30:05")
                                                var parts = result.data.split(' ');
                                                var dateParts = parts[0].split('-');
                                                var timeParts = parts[1].split(':');

                                                expiryDate = new Date(
                                                    parseInt(dateParts[0]), // 年
                                                    parseInt(dateParts[1]) - 1, // 月 (0-11)
                                                    parseInt(dateParts[2]), // 日
                                                    parseInt(timeParts[0]), // 时
                                                    parseInt(timeParts[1]), // 分
                                                    parseInt(timeParts[2])  // 秒
                                                );
                                            }
                                        } catch (parseError) {
                                            console.error("解析日期出错: " + parseError.message);
                                            LogModule.log("解析日期出错: " + parseError.message, "ERROR");

                                            // 尝试最简单的方式显示原始字符串
                                            expiryDateText.setText(result.data);
                                            expiryDateText.setTextColor(android.graphics.Color.parseColor("#FF5722"));
                                            return;
                                        }

                                        //解绑过后设置为已解绑
                                        expiryDateText.setText("已解绑");
                                        // var currentDate = new Date();

                                        // // 比较日期
                                        // if (expiryDate > currentDate) {
                                        //     // 卡密有效
                                        //     expiryDateText.setText(result.data);
                                        //     expiryDateText.setTextColor(android.graphics.Color.parseColor("#FF5722")); // 橙色
                                        // } else {
                                        //     // 卡密已过期
                                        //     expiryDateText.setText("已过期");
                                        //     expiryDateText.setTextColor(android.graphics.Color.parseColor("#F44336")); // 红色
                                        // }
                                    }
                                } catch (e) {
                                    console.error(e.stack);

                                    // 出错时尝试直接显示原始字符串
                                    try {
                                        if (expiryDateText && result.data) {
                                            expiryDateText.setText(result.data);
                                            expiryDateText.setTextColor(android.graphics.Color.parseColor("#FF5722"));
                                        }
                                    } catch (fallbackError) {
                                        console.error("回退显示也失败: " + fallbackError.message);
                                    }
                                }
                            });
                        } else {
                            // 如果没有返回新的到期时间，刷新卡密信息
                            that.queryCardKeyInfo(phone);
                        }
                    } else {
                        let errorMsg = result && result.message ? result.message : "解绑设备失败";
                        that.showToast(errorMsg);
                        LogModule.log("解绑设备响应错误: " + errorMsg, "ERROR");
                    }
                });
            } catch (e) {
                LogModule.log("执行解绑设备操作失败: " + e.message, "ERROR");
                this.showToast("解绑设备出错");
            }
        },

        /**
         * 绑定设备
         * @param {string} phone - 用户手机号
         * @param {string} deviceId - 设备ID
         */
        bindDevice: function (phone, deviceId) {
            try {
                var that = this;

                // 显示加载提示
                this.showToast("正在绑定设备...");

                // 调用API绑定设备
                NetworkModule.bindDevice(phone, deviceId, function (error, result) {
                    if (error) {
                        that.showToast("绑定设备失败: " + error.message);
                        LogModule.log("绑定设备失败: " + error.message, "ERROR");
                        return;
                    }

                    if (result && result.code === 200) {
                        // 如果返回了新的到期时间，直接更新UI
                        if (result.data) {
                            that.showToast("设备绑定成功");

                        } else {
                            // 如果返回false，表示设备已绑定
                            that.showToast("设备已绑定，无需重复绑定");
                            LogModule.log("设备已绑定，无需重复绑定", "INFO");
                        }
                    } else {
                        let errorMsg = result && result.message ? result.message : "绑定设备失败";
                        that.showToast(errorMsg);
                        LogModule.log("绑定设备响应错误: " + errorMsg, "ERROR");
                    }
                });
            } catch (e) {
                LogModule.log("执行绑定设备操作失败: " + e.message, "ERROR");
                this.showToast("绑定设备出错");
            }
        },

        // 在UIModule中添加一个快速返回到脚本中心并切换到"我的"标签的方法
        returnToScriptCenterMyTab: function () {
            try {
                console.log("快速返回到脚本中心并切换到我的标签");

                // 创建脚本中心UI
                this.createScriptCenterUI(false);

                // 立即切换到"我的"标签，不使用setTimeout
                if (ui.myTabBtn) {
                    ui.myTabBtn.performClick(); // 使用performClick()替代click()，更直接触发点击事件
                } else {
                    console.error("未找到我的标签按钮");
                }

                console.log("已返回到脚本中心并切换到我的标签");
            } catch (e) {
                console.error("返回脚本中心并切换到我的标签失败: " + e.message);
                toast("返回失败，请重试");
            }
        },

        /**
         * 设置当前页面
         * @param {string} page - 页面标识
         */
        setCurrentPage: function (page) {
            currentPage = page;
        },

        /**
         * 创建提现页面
         */
        createWithdrawalPage: function () {
            try {
                LogModule.log("创建提现页面");

                // 提现页面布局 - 使用可滚动布局，但不使用RecyclerView
                const withdrawalPageLayoutXml =
                    '<frame>' +
                    '<vertical h="*" w="*">' +
                    '<horizontal gravity="center_vertical" padding="16">' +
                    '<img src="@android:drawable/ic_menu_revert" w="24" h="24" tint="#009688" marginRight="16" id="backBtn"/>' +
                    '<text text="账户提现" textColor="#212121" textSize="18sp" layout_weight="1"/>' +
                    '</horizontal>' +

                    '<scroll id="mainScroll" layout_weight="1">' +
                    '<vertical padding="16">' +
                    '<card cardCornerRadius="8dp" cardElevation="2dp" margin="0 0 0 16">' +
                    '<vertical padding="16">' +
                    '<text text="可提现金额" textColor="#757575" textSize="14sp"/>' +
                    '<text id="availableAmountText" text="0.00元" textColor="#212121" textSize="24sp" textStyle="bold" margin="0 4"/>' +
                    '</vertical>' +
                    '</card>' +

                    '<text text="提现申请" textColor="#212121" textSize="16sp" margin="0 16 0 8"/>' +

                    '<card cardCornerRadius="8dp" cardElevation="2dp" margin="0 8">' +
                    '<vertical padding="16">' +
                    '<horizontal gravity="center_vertical" margin="0 8">' +
                    '<text text="提现金额" textColor="#212121" textSize="16sp" layout_weight="1"/>' +
                    '</horizontal>' +

                    '<vertical bg="#f5f5f5" padding="8" margin="0 8" radius="4dp">' +
                    '<text text="已选择金额" textColor="#757575" textSize="12sp"/>' +
                    '<horizontal gravity="center_vertical">' +
                    '<text text="¥" textColor="#212121" textSize="16sp"/>' +
                    '<text id="selectedAmountText" text="10.00" textColor="#212121" textSize="20sp" layout_weight="1"/>' +
                    '</horizontal>' +
                    '</vertical>' +

                    '<vertical id="amountButtonsContainer" margin="0 8">' +
                    '<!-- 金额按钮将由API动态加载 -->' +
                    '</vertical>' +

                    '<horizontal gravity="center_vertical" margin="0 16 0 8">' +
                    '<text text="支付宝账号" textColor="#212121" textSize="16sp" layout_weight="1"/>' +
                    '</horizontal>' +
                    '<input id="alipayAccountInput" hint="请输入支付宝账号" textSize="14sp" padding="8dp" margin="0 4" bg="#f5f5f5" h="45dp"/>' +

                    '<horizontal gravity="center_vertical" margin="0 16 0 8">' +
                    '<text text="支付宝实名" textColor="#212121" textSize="16sp" layout_weight="1"/>' +
                    '</horizontal>' +
                    '<input id="alipayNameInput" hint="请输入支付宝实名" textSize="14sp" padding="8dp" margin="0 4" bg="#f5f5f5" h="45dp"/>' +

                    '<button id="applyWithdrawalBtn" text="立即提现" textSize="16sp" textColor="#ffffff" bg="#FF5722" margin="0 16 0 8" padding="8dp" h="48dp"/>' +
                    '</vertical>' +
                    '</card>' +

                    '<text text="提现说明" textColor="#212121" textSize="16sp" margin="0 16 0 8"/>' +

                    '<card cardCornerRadius="8dp" cardElevation="2dp" margin="0 8">' +
                    '<vertical padding="16" id="withdrawalInstructionContainer">' +
                    '<text text="加载中..." textColor="#999999" textSize="14sp" gravity="center" padding="16"/>' +
                    '</vertical>' +
                    '</card>' +

                    '<text text="提现记录" textColor="#212121" textSize="16sp" margin="0 16 0 8"/>' +

                    '<card cardCornerRadius="8dp" cardElevation="2dp" margin="0 8 0 16">' +
                    '<vertical id="withdrawalRecordsContainer" padding="8" h="auto">' +
                    '<text text="加载中..." textColor="#999999" textSize="14sp" gravity="center" padding="16"/>' +
                    '</vertical>' +
                    '</card>' +

                    '<frame h="50" />' + // 底部留白，避免内容被遮挡
                    '</vertical>' +
                    '</scroll>' +
                    '</vertical>' +
                    '</frame>';

                // 设置UI布局
                ui.layout(withdrawalPageLayoutXml);

                // 设置当前页面
                this.setCurrentPage("tixian");

                // 绑定按钮事件
                this.bindWithdrawalPageEvents();

                // 加载可提现金额
                this.loadAvailableAmount();

                // 加载提现说明
                this.loadWithdrawalInstruction();

                // 加载提现金额选项
                this.loadWithdrawalAmountOptions();

                // 加载提现记录
                this.loadWithdrawalRecords();

            } catch (e) {
                LogModule.log("创建提现页面失败: " + e.message, "ERROR");
                console.error("创建提现页面失败: " + e.message);
                console.error(e.stack);
                this.showToast("创建提现页面失败: " + e.message);
            }
        },

        /**
         * 初始化金额按钮
         */
        initAmountButtons: function () {
            try {
                console.log("开始初始化金额按钮...");

                // 默认选中金额
                this.currentAmount = 10;

                // 更新显示金额
                ui.selectedAmountText.setText(this.currentAmount.toFixed(2));

                // 初始化金额按钮数组
                this.amountButtons = [
                    { id: "amount1Btn", value: 10 },
                    { id: "amount2Btn", value: 20 },
                    { id: "amount3Btn", value: 30 },
                    { id: "amount4Btn", value: 40 },
                    { id: "amount5Btn", value: 50 },
                    { id: "amount6Btn", value: 100 }
                ];

                // 为按钮绑定点击事件
                const that = this;

                // 第一行按钮
                ui.amount10Btn.on("click", function () {
                    that.updateSelectedAmount(10);
                    that.updateButtonStyles("amount1Btn");
                });

                ui.amount20Btn.on("click", function () {
                    that.updateSelectedAmount(20);
                    that.updateButtonStyles("amount2Btn");
                });

                ui.amount30Btn.on("click", function () {
                    that.updateSelectedAmount(30);
                    that.updateButtonStyles("amount3Btn");
                });

                // 第二行按钮
                ui.amount40Btn.on("click", function () {
                    that.updateSelectedAmount(40);
                    that.updateButtonStyles("amount4Btn");
                });

                ui.amount50Btn.on("click", function () {
                    that.updateSelectedAmount(50);
                    that.updateButtonStyles("amount5Btn");
                });

                ui.amount60Btn.on("click", function () {
                    that.updateSelectedAmount(100);
                    that.updateButtonStyles("amount6Btn");
                });

                // 设置初始按钮样式
                this.updateButtonStyles("amount1Btn");

                console.log("金额按钮初始化完成");
            } catch (e) {
                console.error("初始化金额按钮失败: " + e.message);
                console.error(e.stack);
                LogModule.log("初始化金额按钮失败: " + e.message, "ERROR");
            }
        },

        /**
         * 更新选中金额
         * @param {number} amount - 金额
         */
        updateSelectedAmount: function (amount) {
            try {
                this.currentAmount = amount;
                ui.selectedAmountText.setText(amount.toFixed(2));
                console.log("已更新选中金额: " + amount);
            } catch (e) {
                console.error("更新选中金额失败: " + e.message);
                LogModule.log("更新选中金额失败: " + e.message, "ERROR");
            }
        },

        /**
         * 更新按钮样式
         * @param {string} selectedBtnId - 选中的按钮ID
         */
        updateButtonStyles: function (selectedBtnId) {
            try {
                // 重置所有按钮样式
                ui.amount10Btn.attr("bg", "#ffffff");
                ui.amount10Btn.setTextColor(android.graphics.Color.parseColor("#009688"));

                ui.amount20Btn.attr("bg", "#ffffff");
                ui.amount20Btn.setTextColor(android.graphics.Color.parseColor("#009688"));

                ui.amount30Btn.attr("bg", "#ffffff");
                ui.amount30Btn.setTextColor(android.graphics.Color.parseColor("#009688"));

                ui.amount40Btn.attr("bg", "#ffffff");
                ui.amount40Btn.setTextColor(android.graphics.Color.parseColor("#009688"));

                ui.amount50Btn.attr("bg", "#ffffff");
                ui.amount50Btn.setTextColor(android.graphics.Color.parseColor("#009688"));

                ui.amount60Btn.attr("bg", "#ffffff");
                ui.amount60Btn.setTextColor(android.graphics.Color.parseColor("#009688"));

                // 设置选中按钮样式
                const selectedBtn = this.amountButtons.find(btn => btn.id === selectedBtnId);
                if (selectedBtn) {
                    const btnIndex = this.amountButtons.indexOf(selectedBtn);
                    const uiBtn = btnIndex === 0 ? ui.amount10Btn :
                        btnIndex === 1 ? ui.amount20Btn :
                            btnIndex === 2 ? ui.amount30Btn :
                                btnIndex === 3 ? ui.amount40Btn :
                                    btnIndex === 4 ? ui.amount50Btn :
                                        ui.amount60Btn;

                    uiBtn.attr("bg", "#009688");
                    uiBtn.setTextColor(android.graphics.Color.WHITE);
                    console.log("已设置选中按钮样式: " + selectedBtnId);
                }
            } catch (e) {
                console.error("更新按钮样式失败: " + e.message);
                LogModule.log("更新按钮样式失败: " + e.message, "ERROR");
            }
        },

        /**
         * 绑定提现页面事件
         */
        bindWithdrawalPageEvents: function () {
            const that = UIModule;
            console.log("开始绑定提现页面事件");
            LogModule.log("开始绑定提现页面事件", "INFO");

            // 返回按钮事件
            ui.backBtn.on("click", function () {
                // 返回脚本中心并切换到"我的"标签
                that.returnToScriptCenterMyTab();
            });

            // 提现按钮事件
            if (ui.applyWithdrawalBtn) {
                ui.applyWithdrawalBtn.on("click", function () {
                    try {
                        const alipayAccount = ui.alipayAccountInput.text().trim();
                        const alipayName = ui.alipayNameInput.text().trim();

                        // 表单验证
                        if (!that.currentAmount || that.currentAmount <= 0) {
                            that.showToast("提现金额必须大于0");
                            return;
                        }

                        if (!alipayName) {
                            that.showToast("请输入支付宝实名");
                            return;
                        }

                        // 提交提现申请
                        that.applyWithdrawal(that.currentAmount, alipayAccount, alipayName);
                    } catch (e) {
                        LogModule.log("处理提现申请失败: " + e.message, "ERROR");
                        that.showToast("处理提现申请失败: " + e.message);
                    }
                });
                console.log("提现按钮事件绑定完成");
            } else {
                console.error("提现按钮不存在，无法绑定事件");
            }

            console.log("提现页面事件绑定完成");
            LogModule.log("提现页面事件绑定完成", "INFO");
        },

        /**
         * 加载可提现金额
         */
        loadAvailableAmount: function () {
            const that = this;

            NetworkModule.getAvailableWithdrawalAmount(function (error, result) {
                if (error) {
                    LogModule.log("获取可提现金额失败: " + error.message, "ERROR");
                    that.showToast("获取可提现金额失败");
                    return;
                }
                if (result && result.code === 200 && result.data) {
                    const availableAmount = result.data || 0;
                    ui.availableAmountText.setText(availableAmount.toFixed(2) + "元");
                }
            });
        },

        /**
         * 加载提现说明
         */
        loadWithdrawalInstruction: function () {
            const that = this;

            NetworkModule.getWithdrawalInstruction(function (error, result) {
                if (error) {
                    LogModule.log("获取提现说明失败: " + error.message, "ERROR");
                    return;
                }

                if (result && result.code === 200 && result.data) {
                    // 更新UI内容
                    ui.run(function () {
                        try {
                            // 获取提现说明容器
                            const instructionContainer = ui.findById("withdrawalInstructionContainer");
                            if (instructionContainer) {
                                // 清空容器
                                instructionContainer.removeAllViews();

                                // 将后端返回的说明文本按行分割
                                const instructions = result.data.split("\n");

                                // 为每行说明创建文本视图
                                instructions.forEach(function (instruction) {
                                    if (instruction && instruction.trim()) {
                                        let textView = ui.inflate(
                                            '<text text="' + instruction + '" textColor="#666666" textSize="14sp" margin="0 4"/>',
                                            instructionContainer
                                        );
                                        instructionContainer.addView(textView);
                                    }
                                });

                                LogModule.log("提现说明已更新");
                            } else {
                                console.error("未找到提现说明容器");
                                LogModule.log("未找到提现说明容器", "ERROR");
                            }
                        } catch (uiError) {
                            console.error("更新提现说明UI失败: " + uiError.message);
                            console.error(uiError.stack);
                            LogModule.log("更新提现说明UI失败: " + uiError.message, "ERROR");
                        }
                    });
                } else {
                    LogModule.log("获取提现说明失败: 无效的响应数据");
                }
            });
        },

        /**
         * 加载提现金额选项
         */
        loadWithdrawalAmountOptions: function () {
            const that = this;

            console.log("开始加载提现金额选项...");

            // 默认选中金额
            that.currentAmount = 10;

            // 更新显示金额
            ui.selectedAmountText.setText(that.currentAmount.toFixed(2));

            NetworkModule.getWithdrawalAmountOptions(function (error, result) {
                if (error) {
                    console.error("获取提现金额选项失败: " + error.message);
                    console.error(error.stack);
                    LogModule.log("获取提现金额选项失败: " + error.message, "ERROR");

                    // 使用默认金额选项
                    console.log("由于API错误，使用默认金额选项");
                    that.createDefaultAmountButtons();
                    return;
                }

                console.log("提现金额选项API响应: " + JSON.stringify(result));

                if (result && result.code === 200 && result.data && Array.isArray(result.data)) {
                    console.log("成功获取提现金额选项，数量: " + result.data.length);

                    // 更新UI内容
                    ui.run(function () {
                        try {
                            // 获取金额按钮容器
                            const amountButtonsContainer = ui.findById("amountButtonsContainer");

                            console.log("金额按钮容器: " + (amountButtonsContainer ? "已找到" : "未找到"));

                            if (!amountButtonsContainer) {
                                console.error("未找到金额按钮容器，使用默认金额选项");
                                LogModule.log("未找到金额按钮容器，使用默认金额选项", "ERROR");
                                that.createDefaultAmountButtons();
                                return;
                            }

                            // 清空容器
                            amountButtonsContainer.removeAllViews();

                            // 按sortOrder排序
                            const sortedOptions = result.data.sort((a, b) => a.sortOrder - b.sortOrder);
                            console.log("排序后的金额选项: " + JSON.stringify(sortedOptions));

                            // 清空并重新初始化按钮数组，避免重复添加
                            that.amountButtons = [];

                            // 打印每个金额选项的值，用于调试
                            sortedOptions.forEach((option, index) => {
                                console.log(`金额选项${index + 1}: ${option.amount}元`);
                            });

                            // 创建两个水平布局容器，每行最多放3个按钮
                            let row1 = new android.widget.LinearLayout(context);
                            row1.setOrientation(android.widget.LinearLayout.HORIZONTAL);
                            row1.setLayoutParams(new android.widget.LinearLayout.LayoutParams(
                                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
                            ));
                            row1.setPadding(8, 8, 8, 8);

                            let row2 = new android.widget.LinearLayout(context);
                            row2.setOrientation(android.widget.LinearLayout.HORIZONTAL);
                            row2.setLayoutParams(new android.widget.LinearLayout.LayoutParams(
                                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
                            ));
                            row2.setPadding(8, 8, 8, 8);

                            // 创建每个金额按钮
                            for (let i = 0; i < sortedOptions.length; i++) {
                                // 明确指定当前索引和金额
                                let currentIndex = i;
                                let currentOption = sortedOptions[currentIndex];
                                let currentAmount = currentOption.amount;
                                let currentBtnId = "amountBtn_" + currentIndex;

                                console.log(`创建按钮 ${currentIndex}: 金额=${currentAmount}元, ID=${currentBtnId}`);

                                // 创建按钮
                                let button = new android.widget.Button(context);
                                let buttonParams = new android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1);
                                buttonParams.setMargins(8, 4, 8, 4);
                                button.setLayoutParams(buttonParams);

                                // 设置按钮文本和样式
                                button.setText(currentAmount + "元");
                                button.setTextSize(15);
                                button.setBackgroundColor(android.graphics.Color.parseColor("#ffffff"));
                                button.setTextColor(android.graphics.Color.parseColor("#009688"));

                                // 设置按钮圆角
                                let buttonDrawable = new android.graphics.drawable.GradientDrawable();
                                buttonDrawable.setCornerRadius(16); // 设置圆角半径
                                buttonDrawable.setStroke(2, android.graphics.Color.parseColor("#009688")); // 设置边框
                                buttonDrawable.setColor(android.graphics.Color.parseColor("#ffffff")); // 设置背景色
                                button.setBackground(buttonDrawable);

                                button.setTag(currentBtnId);

                                // 添加按钮到相应的行
                                if (currentIndex < 3) {
                                    row1.addView(button);
                                } else {
                                    row2.addView(button);
                                }

                                // 存储按钮信息
                                that.amountButtons.push({
                                    id: currentBtnId,
                                    value: currentAmount,
                                    view: button
                                });

                                // 创建闭包保存当前值
                                (function (btnAmount, btnId) {
                                    button.setOnClickListener(new android.view.View.OnClickListener({
                                        onClick: function (view) {
                                            console.log(`点击了按钮: 金额=${btnAmount}元, ID=${btnId}`);
                                            that.updateSelectedAmount(btnAmount);
                                            that.updateAmountButtonStyles(btnId);
                                        }
                                    }));
                                })(currentAmount, currentBtnId);
                            }

                            // 添加行到容器
                            amountButtonsContainer.addView(row1);

                            // 只有当第二行有按钮时才添加第二行
                            if (sortedOptions.length > 3) {
                                amountButtonsContainer.addView(row2);
                            }

                            console.log("已创建" + that.amountButtons.length + "个金额按钮");

                            // 默认选中第一个按钮
                            if (that.amountButtons.length > 0) {
                                that.currentAmount = that.amountButtons[0].value;
                                ui.selectedAmountText.setText(that.currentAmount.toFixed(2));
                                that.updateAmountButtonStyles(that.amountButtons[0].id);
                            }

                            LogModule.log("提现金额选项已更新");
                        } catch (uiError) {
                            console.error("更新提现金额选项UI失败: " + uiError.message);
                            console.error(uiError.stack);
                            LogModule.log("更新提现金额选项UI失败: " + uiError.message, "ERROR");

                            // 使用默认金额选项
                            console.log("由于UI错误，使用默认金额选项");
                            that.createDefaultAmountButtons();
                        }
                    });
                } else {
                    console.error("获取提现金额选项失败: 无效的响应数据");
                    console.log("响应数据: " + JSON.stringify(result));
                    LogModule.log("获取提现金额选项失败: 无效的响应数据", "ERROR");

                    // 使用默认金额选项
                    console.log("由于无效响应，使用默认金额选项");
                    that.createDefaultAmountButtons();
                }
            });
        },

        /**
         * 创建默认金额按钮
         */
        createDefaultAmountButtons: function () {
            const that = this;

            console.log("开始创建默认金额按钮...");

            ui.run(function () {
                try {
                    // 获取金额按钮容器
                    const amountButtonsContainer = ui.findById("amountButtonsContainer");

                    console.log("默认金额选项 - 金额按钮容器: " + (amountButtonsContainer ? "已找到" : "未找到"));

                    if (!amountButtonsContainer) {
                        console.error("未找到金额按钮容器，无法创建默认金额按钮");
                        LogModule.log("未找到金额按钮容器，无法创建默认金额按钮", "ERROR");
                        return;
                    }

                    // 清空容器
                    amountButtonsContainer.removeAllViews();

                    // 默认金额选项
                    const defaultOptions = [
                        { amount: 10, sortOrder: 1 },
                        { amount: 20, sortOrder: 2 },
                        { amount: 30, sortOrder: 3 },
                        { amount: 50, sortOrder: 4 },
                        { amount: 100, sortOrder: 5 }
                    ];

                    // 清空并重新初始化按钮数组，避免重复添加
                    that.amountButtons = [];

                    // 创建所有行和按钮
                    const rowCount = Math.ceil(defaultOptions.length / 3); // 每行3个按钮

                    for (let row = 0; row < rowCount; row++) {
                        // 创建一行按钮的容器
                        let horizontalLayout = new android.widget.LinearLayout(context);
                        horizontalLayout.setOrientation(android.widget.LinearLayout.HORIZONTAL);
                        horizontalLayout.setLayoutParams(new android.widget.LinearLayout.LayoutParams(
                            android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                            android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
                        ));
                        horizontalLayout.setPadding(8, 8, 8, 8);

                        // 添加按钮到这一行
                        for (let col = 0; col < 3; col++) {
                            const index = row * 3 + col;
                            if (index < defaultOptions.length) {
                                // 获取当前金额选项
                                const option = defaultOptions[index];
                                const amount = option.amount;

                                // 创建按钮
                                let button = new android.widget.Button(context);
                                let buttonParams = new android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1);
                                buttonParams.setMargins(8, 4, 8, 4);
                                button.setLayoutParams(buttonParams);

                                // 设置按钮文本和样式
                                button.setText(amount + "元");
                                button.setTextSize(15);

                                // 设置按钮圆角
                                let buttonDrawable = new android.graphics.drawable.GradientDrawable();
                                buttonDrawable.setCornerRadius(16); // 设置圆角半径
                                buttonDrawable.setStroke(2, android.graphics.Color.parseColor("#009688")); // 设置边框
                                buttonDrawable.setColor(android.graphics.Color.parseColor("#ffffff")); // 设置背景色
                                button.setBackground(buttonDrawable);

                                button.setTextColor(android.graphics.Color.parseColor("#009688"));

                                // 生成唯一ID
                                const btnId = "defaultAmountBtn_" + index;
                                button.setTag(btnId);

                                // 添加按钮到行
                                horizontalLayout.addView(button);

                                // 存储按钮信息
                                that.amountButtons.push({
                                    id: btnId,
                                    value: amount,
                                    view: button
                                });

                                // 为当前按钮创建一个闭包，确保每个按钮使用正确的金额值
                                (function (currentAmount, currentBtnId) {
                                    button.setOnClickListener(new android.view.View.OnClickListener({
                                        onClick: function (view) {
                                            that.updateSelectedAmount(currentAmount);
                                            that.updateAmountButtonStyles(currentBtnId);
                                        }
                                    }));
                                })(amount, btnId);

                                console.log(`创建了默认金额按钮${index}: ${amount}元, ID: ${btnId}`);
                            } else {
                                // 添加空白占位
                                let frame = new android.widget.FrameLayout(context);
                                let frameParams = new android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1);
                                frameParams.setMargins(8, 4, 8, 4);
                                frame.setLayoutParams(frameParams);
                                horizontalLayout.addView(frame);
                            }
                        }

                        // 添加行到容器
                        amountButtonsContainer.addView(horizontalLayout);
                    }

                    console.log("已创建" + that.amountButtons.length + "个默认金额按钮");

                    // 默认选中第一个按钮
                    if (that.amountButtons.length > 0) {
                        that.currentAmount = that.amountButtons[0].value;
                        ui.selectedAmountText.setText(that.currentAmount.toFixed(2));
                        that.updateAmountButtonStyles(that.amountButtons[0].id);
                    }

                    LogModule.log("默认提现金额选项已创建");
                } catch (e) {
                    console.error("创建默认金额按钮失败: " + e.message);
                    console.error(e.stack);
                    LogModule.log("创建默认金额按钮失败: " + e.message, "ERROR");
                }
            });
        },

        /**
         * 更新选中金额
         * @param {number} amount - 金额
         */
        updateSelectedAmount: function (amount) {
            try {
                this.currentAmount = amount;
                ui.selectedAmountText.setText(amount.toFixed(2));
                console.log("已更新选中金额: " + amount);
            } catch (e) {
                console.error("更新选中金额失败: " + e.message);
                LogModule.log("更新选中金额失败: " + e.message, "ERROR");
            }
        },

        /**
         * 更新金额按钮样式
         * @param {string} selectedBtnId - 选中的按钮ID
         */
        updateAmountButtonStyles: function (selectedBtnId) {
            try {
                console.log("更新金额按钮样式，选中: " + selectedBtnId);

                // 重置所有按钮样式
                this.amountButtons.forEach(function (btn) {
                    try {
                        if (btn.view) {
                            // 创建未选中状态的drawable
                            let normalDrawable = new android.graphics.drawable.GradientDrawable();
                            normalDrawable.setCornerRadius(16); // 设置圆角半径
                            normalDrawable.setStroke(2, android.graphics.Color.parseColor("#009688")); // 设置边框
                            normalDrawable.setColor(android.graphics.Color.parseColor("#ffffff")); // 设置背景色
                            btn.view.setBackground(normalDrawable);
                            btn.view.setTextColor(android.graphics.Color.parseColor("#009688"));
                        }
                    } catch (btnError) {
                        console.error("重置按钮" + btn.id + "样式失败: " + btnError.message);
                    }
                });

                // 设置选中按钮样式
                const selectedBtn = this.amountButtons.find(btn => btn.id === selectedBtnId);
                if (selectedBtn && selectedBtn.view) {
                    // 创建选中状态的drawable
                    let selectedDrawable = new android.graphics.drawable.GradientDrawable();
                    selectedDrawable.setCornerRadius(16); // 设置圆角半径
                    selectedDrawable.setColor(android.graphics.Color.parseColor("#009688")); // 设置背景色
                    selectedBtn.view.setBackground(selectedDrawable);
                    selectedBtn.view.setTextColor(android.graphics.Color.WHITE);
                    console.log("已设置选中按钮样式: " + selectedBtnId);
                } else {
                    console.error("未找到选中的按钮: " + selectedBtnId);
                }
            } catch (e) {
                console.error("更新金额按钮样式失败: " + e.message);
                console.error(e.stack);
                LogModule.log("更新金额按钮样式失败: " + e.message, "ERROR");
            }
        },

        /**
         * 加载提现记录
         */
        loadWithdrawalRecords: function () {
            const that = this;

            NetworkModule.getWithdrawalRecords(function (error, result) {
                if (error) {
                    LogModule.log("获取提现记录失败: " + error.message, "ERROR");
                    that.showToast("获取提现记录失败");
                    ui.run(function () {
                        try {
                            ui.withdrawalRecordsContainer.removeAllViews();
                            let errorView = ui.inflate(
                                '<text text="获取提现记录失败" textColor="#F44336" textSize="14sp" gravity="center" padding="16"/>',
                                ui.withdrawalRecordsContainer
                            );
                            ui.withdrawalRecordsContainer.addView(errorView);
                        } catch (e) {
                            console.error("显示提现记录错误信息失败: " + e.message);
                            LogModule.log("显示提现记录错误信息失败: " + e.message, "ERROR");
                        }
                    });
                    return;
                }

                ui.run(function () {
                    try {
                        // 清空容器
                        ui.withdrawalRecordsContainer.removeAllViews();

                        if (!result || !result.code === 200 || !result.data || result.data.length === 0) {
                            // 处理空记录情况
                            let emptyView = ui.inflate(
                                '<text text="暂无提现记录" textColor="#999999" textSize="14sp" gravity="center" padding="16"/>',
                                ui.withdrawalRecordsContainer
                            );
                            ui.withdrawalRecordsContainer.addView(emptyView);
                            return;
                        }

                        // 限制最多显示10条记录
                        const maxRecords = Math.min(result.data.length, 10);

                        // 创建一个函数来添加记录项
                        const addRecordItem = function (index) {
                            if (index >= result.data.length) return;

                            const recordItem = result.data[index];

                            // 处理状态颜色
                            let statusColor = "#FFC107"; // 默认黄色 (待审核)
                            switch (recordItem.status) {
                                case 1: // 已通过
                                    statusColor = "#4CAF50";
                                    break;
                                case 2: // 已拒绝
                                    statusColor = "#F44336";
                                    break;
                                case 3: // 已打款
                                    statusColor = "#2196F3";
                                    break;
                                default:
                                    statusColor = "#FFC107";
                                    break;
                            }

                            // 创建记录项视图
                            let recordView = ui.inflate(
                                '<vertical padding="12" w="*">' +
                                '<horizontal>' +
                                '<vertical layout_weight="1">' +
                                '<text id="amountText" text="" textColor="#212121" textSize="16sp"/>' +
                                '<text id="timeText" text="" textColor="#757575" textSize="14sp" margin="0 4"/>' +
                                '</vertical>' +
                                '<text id="statusText" text="" textSize="14sp"/>' +
                                '</horizontal>' +
                                '</vertical>',
                                ui.withdrawalRecordsContainer
                            );

                            // 设置记录项数据
                            recordView.amountText.setText(recordItem.amount + "元");
                            recordView.timeText.setText(recordItem.createTime || "");
                            recordView.statusText.setText(recordItem.statusName || "");
                            recordView.statusText.setTextColor(android.graphics.Color.parseColor(statusColor));

                            // 添加到容器
                            ui.withdrawalRecordsContainer.addView(recordView);

                            // 如果不是最后一项，添加分隔线
                            if (index < result.data.length - 1) {
                                let dividerView = ui.inflate(
                                    '<frame h="1" w="*" bg="#E0E0E0" margin="12 8 12 0"/>',
                                    ui.withdrawalRecordsContainer
                                );
                                ui.withdrawalRecordsContainer.addView(dividerView);
                            }
                        };

                        // 添加初始记录
                        for (let i = 0; i < maxRecords; i++) {
                            addRecordItem(i);
                        }

                        // 如果有更多记录，显示"查看更多"按钮
                        if (result.data.length > maxRecords) {
                            let moreView = ui.inflate(
                                '<button text="查看更多" textColor="#2196F3" textSize="14sp" style="Widget.AppCompat.Button.Borderless" w="*" padding="8"/>',
                                ui.withdrawalRecordsContainer
                            );
                            ui.withdrawalRecordsContainer.addView(moreView);

                            // 添加点击事件
                            moreView.on("click", function () {
                                try {
                                    LogModule.log("用户点击查看更多提现记录", "INFO");

                                    // 移除"查看更多"按钮
                                    ui.withdrawalRecordsContainer.removeView(moreView);

                                    // 计算要显示的额外记录数量
                                    const totalRecords = result.data.length;
                                    const currentShown = maxRecords;
                                    const remainingRecords = totalRecords - currentShown;

                                    // 添加所有剩余记录
                                    for (let i = currentShown; i < totalRecords; i++) {
                                        addRecordItem(i);
                                    }

                                    that.showToast("已显示全部提现记录");
                                } catch (clickError) {
                                    console.error("处理查看更多点击事件失败: " + clickError.message);
                                    LogModule.log("处理查看更多点击事件失败: " + clickError.message, "ERROR");
                                    that.showToast("加载更多记录失败");
                                }
                            });
                        }
                    } catch (e) {
                        console.error("设置提现记录失败: " + e.message);
                        console.error(e.stack);
                        LogModule.log("设置提现记录失败: " + e.message, "ERROR");
                        LogModule.log(e.stack, "ERROR");

                        // 显示错误信息
                        ui.withdrawalRecordsContainer.removeAllViews();
                        let errorView = ui.inflate(
                            '<text text="加载提现记录出错" textColor="#F44336" textSize="14sp" gravity="center" padding="16"/>',
                            ui.withdrawalRecordsContainer
                        );
                        ui.withdrawalRecordsContainer.addView(errorView);
                    }
                });
            });
        },

        /**
         * 申请提现
         * @param {number} amount - 提现金额
         * @param {string} alipayAccount - 支付宝账号
         * @param {string} alipayName - 支付宝实名
         */
        applyWithdrawal: function (amount, alipayAccount, alipayName) {
            const that = this;

            // 显示加载对话框
            that.showDialog("提交中", "正在提交提现申请，请稍候...");

            NetworkModule.applyWithdrawal(amount, alipayAccount, alipayName, function (error, result) {
                // 关闭加载对话框
                that.dismissDialog();

                if (error) {
                    LogModule.log("申请提现失败: " + error.message, "ERROR");
                    that.showToast("申请提现失败: " + error.message);
                    return;
                }

                if (result && result.code === 200) {
                    that.showToast("提现申请已提交");

                    // 重新加载数据
                    that.loadAvailableAmount();
                    that.loadWithdrawalRecords();
                } else {
                    const errorMsg = result && result.message ? result.message : "未知错误";
                    LogModule.log("申请提现失败: " + errorMsg, "ERROR");
                    that.showToast("申请提现失败: " + errorMsg);
                }
            });
        },

        /**
         * 创建主UI
         */
        createMainUI: function () {
            try {
                // 创建UI布局
                var layoutXml =
                    '<frame>' +
                    '<vertical padding="16">' +
                    '<text id="appTitle" text="脚本助手" textSize="24sp" textColor="#3F51B5" gravity="center" margin="0 10"/>' +

                    '<card margin="10" cardCornerRadius="8dp" cardElevation="5dp">' +
                    '<img id="bannerImage" src="@drawable/ic_launcher" scaleType="centerCrop" h="150"/>' +
                    '</card>' +

                    '<card margin="10" cardCornerRadius="8dp" cardElevation="3dp">' +
                    '<vertical padding="16">' +
                    '<horizontal gravity="center_vertical">' +
                    '<text text="无障碍" textSize="16sp" layout_weight="1"/>' +
                    '<text text="(点击、滑动、长按等)" textSize="12sp" textColor="#888888" layout_weight="2"/>' +
                    '<Switch id="accessibilitySwitch" checked="false"/>' +
                    '</horizontal>' +

                    '<horizontal gravity="center_vertical" marginTop="20">' +
                    '<text text="悬浮框" textSize="16sp" layout_weight="1"/>' +
                    '<text text="(增加脚本存活率)" textSize="12sp" textColor="#888888" layout_weight="2"/>' +
                    '<Switch id="floatingWindowSwitch" checked="false"/>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</card>' +

                    '<horizontal margin="10 20">' +
                    '<button id="loginBtn" text="登录" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                    '<button id="registerBtn" text="注册" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="5"/>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</frame>';

                // 设置UI布局
                ui.layout(layoutXml);

                // 绑定事件
                this.bindEvents();

                // 初始化状态
                this.updateUIState();

                // 更新当前页面状态
                this.setCurrentPage("main");
                LogModule.log("主界面创建成功");
            } catch (e) {
                LogModule.log("创建主界面失败: " + e.message, "ERROR");
                console.error("创建主界面失败: " + e.message);
                console.error(e.stack);
            }
        },

        /**
         * 显示Toast提示
         * @param {string} message - 提示消息
         */
        showToast: function (message) {
            try {
                toast(message);
            } catch (e) {
                console.error("显示Toast失败: " + e.message);
            }
        },

        /**
         * 显示对话框
         * @param {string} title - 标题
         * @param {string} content - 内容
         */
        showDialog: function (title, content) {
            try {
                if (this.loadingDialog) {
                    this.dismissDialog();
                }

                this.loadingDialog = dialogs.build({
                    title: title,
                    content: content,
                    cancelable: false
                });

                this.loadingDialog.show();
            } catch (e) {
                LogModule.log("显示对话框失败: " + e.message, "ERROR");
                console.error("显示对话框失败: " + e.message);
            }
        },

        /**
         * 关闭对话框
         */
        dismissDialog: function () {
            try {
                if (this.loadingDialog && this.loadingDialog.isShowing()) {
                    this.loadingDialog.dismiss();
                }
                this.loadingDialog = null;
            } catch (e) {
                LogModule.log("关闭对话框失败: " + e.message, "ERROR");
                console.error("关闭对话框失败: " + e.message);
            }
        },

        /**
         * 屏蔽手机号中间四位数字
         * @param {string} phone - 手机号
         */
        maskPhone: function (phone) {
            if (!phone || phone.length < 11) {
                return phone;
            }
            return phone.substring(0, 3) + "****" + phone.substring(7);
        },

        /**
         * 创建微信阅读脚本配置页面
         */
        createWxReadConfigPage: function () {
            try {
                console.log("创建微信阅读配置页面");
                LogModule.log("创建微信阅读配置页面", "INFO");
                var that = this;

                // 微信阅读配置页面布局
                const wxReadConfigLayoutXml =
                    '<frame>' +
                    '<vertical h="*" w="*" bg="#f5ffe0">' +
                    '<horizontal gravity="center_vertical" padding="16" bg="#009688">' +
                    '<img src="@android:drawable/ic_menu_revert" w="24" h="24" tint="#ffffff" marginRight="16" id="wxReadBackBtn"/>' +
                    '<text text="微信阅读" textColor="#ffffff" textSize="18sp" layout_weight="1"/>' +
                    '</horizontal>' +

                    '<scroll layout_weight="1">' +
                    '<vertical padding="16">' +
                    
                    // 文章滑动配置
                    '<card cardCornerRadius="8dp" cardElevation="2dp" margin="0 0 0 16" bg="#ffff8a">' +
                    '<text text="文章滑动配置" textSize="16sp" textColor="#333333" padding="12 8" textStyle="bold"/>' +
                    '</card>' +
                    
                    '<vertical margin="0 0 16 0">' +
                    '<horizontal gravity="center_vertical" padding="12">' +
                    '<text text="每篇文章滑动" textSize="14sp" textColor="#333333" layout_weight="1"/>' +
                    '<input id="wxReadScrollCount" text="8" inputType="number" textSize="14sp" w="80dp" gravity="center" bg="#ffffff" padding="8"/>' +
                    '<text text="次" textSize="14sp" textColor="#333333" marginLeft="8"/>' +
                    '</horizontal>' +
                    
                    '<horizontal gravity="center_vertical" padding="12">' +
                    '<text text="每次滑动后停留" textSize="14sp" textColor="#333333" layout_weight="1"/>' +
                    '<input id="wxReadScrollDelay" text="3" inputType="number" textSize="14sp" w="80dp" gravity="center" bg="#ffffff" padding="8"/>' +
                    '<text text="秒" textSize="14sp" textColor="#333333" marginLeft="8"/>' +
                    '</horizontal>' +
                    
                    '<horizontal gravity="center_vertical" padding="12">' +
                    '<text text="点赞概率" textSize="14sp" textColor="#333333" layout_weight="1"/>' +
                    '<input id="wxReadLikeProbability" text="20" inputType="number" textSize="14sp" w="80dp" gravity="center" bg="#ffffff" padding="8"/>' +
                    '<text text="%" textSize="14sp" textColor="#333333" marginLeft="8"/>' +
                    '</horizontal>' +
                    
                    '<horizontal gravity="center_vertical" padding="12">' +
                    '<text text="喜欢概率" textSize="14sp" textColor="#333333" layout_weight="1"/>' +
                    '<input id="wxReadFavoriteProbability" text="20" inputType="number" textSize="14sp" w="80dp" gravity="center" bg="#ffffff" padding="8"/>' +
                    '<text text="%" textSize="14sp" textColor="#333333" marginLeft="8"/>' +
                    '</horizontal>' +
                    '</vertical>' +
                    
                    // 启动选项
                    '<card cardCornerRadius="8dp" cardElevation="2dp" margin="0 0 0 16" bg="#ffff8a">' +
                    '<text text="启动选项" textSize="16sp" textColor="#333333" padding="12 8" textStyle="bold"/>' +
                    '</card>' +
                    
                    '<vertical margin="0 0 16 0">' +
                    '<horizontal gravity="center_vertical" padding="12">' +
                    '<checkbox id="wxReadSimulateHuman" text="是否模拟人工进行滑动" textSize="14sp" textColor="#333333" checked="true"/>' +
                    '</horizontal>' +
                    '</vertical>' +
                    
                    // 启动按钮
                    '<button id="wxReadStartBtn" text="启动脚本" textSize="16sp" textColor="#ffffff" bg="#009688" margin="16" padding="16" textStyle="bold"/>' +

                    // 启动提示文字
                    '<text text="点击启动后会自动打开微信，请手动进入到第一篇文章页面，然后点击开始。" textSize="12sp" textColor="#666666" margin="16 0 16 16" gravity="center"/>' +


                    '</vertical>' +
                    '</scroll>' +
                    '</vertical>' +
                    '</frame>';

                // 创建并显示页面
                ui.layout(wxReadConfigLayoutXml);

                // 为输入框添加数字验证
                that.setupNumberInputValidation();

                // 绑定返回按钮事件
                ui.wxReadBackBtn.on("click", function () {
                    that.createScriptCenterUI(false);
                });

                // 绑定启动按钮事件
                ui.wxReadStartBtn.on("click", function () {
                    that.startWxReadScript();
                });

                that.setCurrentPage("wxReadConfig");
                console.log("微信阅读配置页面创建完成");

            } catch (e) {
                console.error("创建微信阅读配置页面失败: " + e.message);
                LogModule.log("创建微信阅读配置页面失败: " + e.message, "ERROR");
                that.showToast("页面创建失败，请重试");
            }
        },

        /**
         * 为输入框设置数字验证
         */
        setupNumberInputValidation: function () {
            var numberInputs = ["wxReadScrollCount", "wxReadScrollDelay", "wxReadLikeProbability", "wxReadFavoriteProbability"];
            
            numberInputs.forEach(function (inputId) {
                if (ui[inputId]) {
                    ui[inputId].on("text_changed", function (text) {
                        // 只允许数字输入
                        var numericText = text.replace(/[^0-9]/g, '');
                        if (text !== numericText) {
                            ui[inputId].setText(numericText);
                        }
                    });
                }
            });
        },

        /**
         * 启动微信阅读脚本
         */
        startWxReadScript: function () {
            try {
                var that = this;

                // 获取配置参数
                var scrollCount = parseInt(ui.wxReadScrollCount.text()) || 8;
                var scrollDelay = parseInt(ui.wxReadScrollDelay.text()) || 3;
                var likeProbability = parseInt(ui.wxReadLikeProbability.text()) || 20;
                var favoriteProbability = parseInt(ui.wxReadFavoriteProbability.text()) || 20;
                var simulateHuman = ui.wxReadSimulateHuman.checked;

                // 参数验证
                if (scrollCount < 1 || scrollCount > 50) {
                    that.showToast("每篇文章滑动次数应在1-50之间");
                    return;
                }

                if (scrollDelay < 1 || scrollDelay > 30) {
                    that.showToast("滑动停留时间应在1-30秒之间");
                    return;
                }

                if (likeProbability < 0 || likeProbability > 100) {
                    that.showToast("点赞概率应在0-100%之间");
                    return;
                }

                if (favoriteProbability < 0 || favoriteProbability > 100) {
                    that.showToast("喜欢概率应在0-100%之间");
                    return;
                }

                // 保存配置参数到全局变量
                that.wxReadConfig = {
                    scrollCount: scrollCount,
                    scrollDelay: scrollDelay,
                    likeProbability: likeProbability,
                    favoriteProbability: favoriteProbability,
                    simulateHuman: simulateHuman
                };

                // 显示配置信息
                var configInfo = "配置信息：\n" +
                    "每篇文章滑动：" + scrollCount + "次\n" +
                    "滑动停留时间：" + scrollDelay + "秒\n" +
                    "点赞概率：" + likeProbability + "%\n" +
                    "喜欢概率：" + favoriteProbability + "%\n" +
                    "模拟人工滑动：" + (simulateHuman ? "是" : "否");

                console.log("微信阅读脚本配置：", configInfo);
                LogModule.log("微信阅读脚本启动，配置：" + configInfo, "INFO");

                // 显示确认对话框
                dialogs.build({
                    title: "确认启动",
                    content: configInfo + "\n\n确定要启动微信阅读脚本吗？",
                    positive: "启动",
                    negative: "取消"
                }).on("positive", function () {
                    that.showToast("脚本启动中...");
                    // 创建控制按钮界面
                    that.createWxReadControlButtons();
                }).show();

            } catch (e) {
                console.error("启动微信阅读脚本失败: " + e.message);
                LogModule.log("启动微信阅读脚本失败: " + e.message, "ERROR");
                that.showToast("启动失败，请重试");
            }
        },

        /**
         * 创建微信阅读脚本控制按钮
         */
        createWxReadControlButtons: function () {
            try {
                var that = this;

                // 初始化脚本状态
                that.wxReadScriptRunning = false;
                that.wxReadScriptPaused = false;
                that.wxReadConsoleVisible = false;

                // 创建悬浮控制按钮
                that.wxReadControlWindow = floaty.window(
                    '<vertical>' +
                    '<button id="wxReadStartPauseBtn" text="启动" textSize="12sp" textColor="#ffffff" bg="#4CAF50" w="60dp" h="35dp" margin="2dp"/>' +
                    '<button id="wxReadLogBtn" text="日志" textSize="12sp" textColor="#ffffff" bg="#2196F3" w="60dp" h="35dp" margin="2dp"/>' +
                    '<button id="wxReadStopBtn" text="结束" textSize="12sp" textColor="#ffffff" bg="#F44336" w="60dp" h="35dp" margin="2dp"/>' +
                    '</vertical>'
                );

                // 设置悬浮窗位置（左侧位置，类似照片中的位置）
                that.wxReadControlWindow.setPosition(50, device.height / 2 - 100);

                // 绑定启动/暂停按钮事件
                that.wxReadControlWindow.wxReadStartPauseBtn.on("click", function () {
                    if (!that.wxReadScriptRunning) {
                        // 启动脚本
                        that.startWxReadExecution();
                        that.wxReadControlWindow.wxReadStartPauseBtn.setText("暂停");
                        that.wxReadControlWindow.wxReadStartPauseBtn.attr("bg", "#FF9800");
                        that.wxReadScriptRunning = true;
                        that.wxReadScriptPaused = false;
                        toast("微信阅读脚本已启动");
                    } else if (that.wxReadScriptPaused) {
                        // 继续执行
                        that.wxReadScriptPaused = false;
                        that.wxReadControlWindow.wxReadStartPauseBtn.setText("暂停");
                        that.wxReadControlWindow.wxReadStartPauseBtn.attr("bg", "#FF9800");
                        toast("脚本继续执行");
                    } else {
                        // 暂停脚本
                        that.wxReadScriptPaused = true;
                        that.wxReadControlWindow.wxReadStartPauseBtn.setText("启动");
                        that.wxReadControlWindow.wxReadStartPauseBtn.attr("bg", "#4CAF50");
                        toast("脚本已暂停");
                    }
                });

                // 绑定日志按钮事件
                that.wxReadControlWindow.wxReadLogBtn.on("click", function () {
                    if (!that.wxReadConsoleVisible) {
                        // 显示控制台
                        console.show();
                        that.wxReadControlWindow.wxReadLogBtn.setText("隐藏");
                        that.wxReadConsoleVisible = true;
                        toast("控制台已显示");
                    } else {
                        // 隐藏控制台
                        console.hide();
                        that.wxReadControlWindow.wxReadLogBtn.setText("日志");
                        that.wxReadConsoleVisible = false;
                        toast("控制台已隐藏");
                    }
                });

                // 绑定结束按钮事件
                that.wxReadControlWindow.wxReadStopBtn.on("click", function () {
                    // 结束脚本执行
                    that.stopWxReadExecution();
                    // 关闭悬浮窗
                    if (that.wxReadControlWindow) {
                        that.wxReadControlWindow.close();
                        that.wxReadControlWindow = null;
                    }
                    // 隐藏控制台
                    console.hide();
                    toast("微信阅读脚本已结束");
                });

                console.log("微信阅读脚本控制按钮创建完成");
                LogModule.log("微信阅读脚本控制按钮创建完成", "INFO");

            } catch (e) {
                console.error("创建微信阅读脚本控制按钮失败: " + e.message);
                LogModule.log("创建微信阅读脚本控制按钮失败: " + e.message, "ERROR");
                that.showToast("创建控制按钮失败");
            }
        },

        /**
         * 开始执行微信阅读脚本
         */
        startWxReadExecution: function () {
            try {
                var that = this;

                console.log("========== 开始执行微信阅读脚本 ==========");
                console.log("配置参数:", JSON.stringify(that.wxReadConfig));
                LogModule.log("微信阅读脚本开始执行", "INFO");

                // 在新线程中执行脚本逻辑
                threads.start(function () {
                    that.wxReadExecutionLoop();
                });

            } catch (e) {
                console.error("启动微信阅读脚本执行失败: " + e.message);
                LogModule.log("启动微信阅读脚本执行失败: " + e.message, "ERROR");
            }
        },

        /**
         * 微信阅读脚本执行循环
         */
        wxReadExecutionLoop: function () {
            var that = this;

            try {
                while (that.wxReadScriptRunning) {
                    // 检查暂停状态
                    while (that.wxReadScriptPaused && that.wxReadScriptRunning) {
                        console.log("脚本已暂停，等待恢复...");
                        sleep(1000);
                    }

                    // 检查是否仍在运行
                    if (!that.wxReadScriptRunning) {
                        break;
                    }

                    // 执行微信阅读逻辑
                    console.log("执行微信阅读操作...");

                    // 调用现有的微信消息识别功能
                    var messages = getWeChatMessages();
                    if (messages && messages.length > 0) {
                        console.log("识别到消息:", messages.join('\n'));
                        LogModule.log("识别到消息: " + messages.join(' | '), "INFO");
                    } else {
                        console.log("未识别到消息内容");
                    }

                    // 模拟滑动操作
                    if (that.wxReadConfig.simulateHuman) {
                        for (var i = 0; i < that.wxReadConfig.scrollCount; i++) {
                            if (!that.wxReadScriptRunning || that.wxReadScriptPaused) break;

                            // 随机滑动距离，模拟人工操作
                            var startY = device.height * (0.6 + Math.random() * 0.2);
                            var endY = device.height * (0.2 + Math.random() * 0.2);
                            var duration = 300 + Math.random() * 200;

                            swipe(device.width / 2, startY, device.width / 2, endY, duration);
                            console.log("执行滑动操作 " + (i + 1) + "/" + that.wxReadConfig.scrollCount);

                            // 滑动后停留
                            sleep(that.wxReadConfig.scrollDelay * 1000);
                        }
                    }

                    // 操作间隔
                    sleep(2000);
                }

                console.log("微信阅读脚本执行循环结束");
                LogModule.log("微信阅读脚本执行循环结束", "INFO");

            } catch (e) {
                console.error("微信阅读脚本执行异常: " + e.message);
                LogModule.log("微信阅读脚本执行异常: " + e.message, "ERROR");
            }
        },

        /**
         * 停止微信阅读脚本执行
         */
        stopWxReadExecution: function () {
            try {
                var that = this;

                that.wxReadScriptRunning = false;
                that.wxReadScriptPaused = false;
                that.wxReadConsoleVisible = false;

                console.log("微信阅读脚本已停止");
                LogModule.log("微信阅读脚本已停止", "INFO");

            } catch (e) {
                console.error("停止微信阅读脚本失败: " + e.message);
                LogModule.log("停止微信阅读脚本失败: " + e.message, "ERROR");
            }
        }
    };
})();

// 添加积分模块
const pointsModule = (function () {
    return {
        /**
         * 显示积分页面
         */
        showPointsPage: function () {
            try {
                console.log("开始显示积分页面");

                // 使用纯字符串形式创建UI，避免JSX解析问题
                var pointsPageLayoutXml =
                    '<frame bg="#f5ffe0">' +
                    '<vertical padding="8" h="*">' +
                    '<horizontal gravity="center_vertical" h="50">' +
                    '<button id="backFromPointsBtn" text="返回" textSize="14sp" style="Widget.AppCompat.Button.Borderless" w="60dp" h="40dp"/>' +
                    '<text text="我的积分" textSize="18sp" textColor="#333333" gravity="center" layout_weight="1" textStyle="bold"/>' +
                    '<frame w="60dp" visibility="invisible"/>' +
                    '</horizontal>' +

                    '<scroll layout_weight="1">' +
                    '<vertical padding="10">' +
                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                    '<vertical padding="15">' +
                    '<text text="积分概览" textSize="16sp" textColor="#333333" textStyle="bold"/>' +
                    '<horizontal margin="0 10 0 5">' +
                    '<vertical layout_weight="1" gravity="center">' +
                    '<text id="currentPointsText" text="0" textSize="20sp" textColor="#FF5722" textStyle="bold"/>' +
                    '<text text="当前积分" textSize="14sp" textColor="#666666" marginTop="5"/>' +
                    '</vertical>' +
                    '<vertical layout_weight="1" gravity="center">' +
                    '<text id="totalEarnedPointsText" text="0" textSize="20sp" textColor="#4CAF50" textStyle="bold"/>' +
                    '<text text="累计获得" textSize="14sp" textColor="#666666" marginTop="5"/>' +
                    '</vertical>' +
                    '<vertical layout_weight="1" gravity="center">' +
                    '<text id="usedPointsText" text="0" textSize="20sp" textColor="#2196F3" textStyle="bold"/>' +
                    '<text text="已使用" textSize="14sp" textColor="#666666" marginTop="5"/>' +
                    '</vertical>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</card>' +

                    '<card cardCornerRadius="10dp" cardElevation="2dp" margin="5 10">' +
                    '<vertical padding="15">' +
                    '<text text="积分记录" textSize="16sp" textColor="#333333" textStyle="bold"/>' +
                    '<vertical id="pointsRecordContainer" margin="0 10">' +
                    '<text text="加载中..." textSize="14sp" textColor="#999999" gravity="center" margin="0 20"/>' +
                    '</vertical>' +
                    '</vertical>' +
                    '</card>' +
                    '</vertical>' +
                    '</scroll>' +
                    '</vertical>' +
                    '</frame>';

                try {
                    console.log("准备设置UI布局");
                    // 使用ui.run确保在UI线程中执行
                    ui.run(function () {
                        try {
                            // 设置UI布局
                            ui.layout(pointsPageLayoutXml);

                            // 设置当前页面状态
                            UIModule.setCurrentPage("points");

                            console.log("积分页面UI布局设置成功");
                        } catch (e) {
                            console.error("设置积分页面UI布局失败: " + e.message);
                            toast("设置积分页面UI布局失败: " + e.message);
                        }
                    });
                } catch (layoutError) {
                    console.error("UI线程执行布局设置失败: " + layoutError.message);
                    toast("UI线程执行布局设置失败");
                    return;
                }

                // 延迟执行事件绑定和数据加载，确保UI已完全加载
                setTimeout(function () {
                    try {
                        console.log("开始绑定返回按钮事件");
                        // 绑定返回按钮事件
                        var backBtn = ui.findById("backFromPointsBtn");
                        if (backBtn) {
                            backBtn.on("click", function () {
                                console.log("积分页面返回按钮被点击");
                                // 直接返回脚本中心并切换到"我的"标签
                                UIModule.returnToScriptCenterMyTab();
                                return true;
                            });
                            console.log("积分页面返回按钮事件绑定成功");
                        } else {
                            console.error("找不到积分页面返回按钮元素");
                            toast("找不到返回按钮，界面可能未正确加载");
                        }

                        // 加载积分数据
                        console.log("开始加载积分数据");
                        pointsModule.loadPointsData();

                    } catch (bindError) {
                        console.error("绑定积分页面事件失败: " + bindError.message);
                        toast("绑定积分页面事件失败");
                    }
                }, 100); // 减少延迟时间，提高响应速度

                LogModule.log("积分页面已显示");
            } catch (e) {
                console.error("显示积分页面失败: " + e.message);
                console.error(e.stack);
                toast("显示积分页面失败: " + e.message);
            }
        },

        /**
         * 加载积分数据
         */
        loadPointsData: function () {
            try {
                console.log("开始加载积分数据...");
                var that = this;
                var userPhone = ConfigModule.get("userPhone");
                var token = ConfigModule.get("userToken");

                if (!userPhone || !token) {
                    console.log("用户信息不完整，无法加载积分数据");
                    toast("用户信息不完整，无法加载积分数据");
                    LogModule.log("加载积分数据失败：用户信息不完整", "ERROR");
                    return;
                }


                // 调用API获取积分数据
                NetworkModule.post("/points/external/user", { phone: userPhone }, function (error, result) {
                    if (error) {
                        console.error("获取积分数据失败: " + error.message);
                        toast("获取积分数据失败，请检查网络连接");
                        LogModule.log("获取积分数据失败: " + error.message, "ERROR");
                        return;
                    }

                    if (result && result.code === 200 && result.data) {
                        console.log("积分数据获取成功，开始更新UI");

                        // 更新UI - 使用ui.run确保在UI线程中执行
                        ui.run(function () {
                            try {
                                var pointsData = result.data;
                                var userPoints = pointsData.userPoints || {};
                                var pointsRecords = pointsData.pointsRecords || [];

                                // 更新积分概览
                                var currentPointsText = ui.findById("currentPointsText");
                                var totalEarnedPointsText = ui.findById("totalEarnedPointsText");
                                var usedPointsText = ui.findById("usedPointsText");

                                if (currentPointsText) {
                                    currentPointsText.setText(userPoints.points ? userPoints.points.toString() : "0");
                                }

                                if (totalEarnedPointsText) {
                                    totalEarnedPointsText.setText(userPoints.totalEarnedPoints ? userPoints.totalEarnedPoints.toString() : "0");
                                }

                                if (usedPointsText) {
                                    usedPointsText.setText(userPoints.totalConsumedPoints ? userPoints.totalConsumedPoints.toString() : "0");
                                }

                                // 更新积分记录
                                var recordContainer = ui.findById("pointsRecordContainer");
                                if (!recordContainer) {
                                    console.error("找不到积分记录容器");
                                    toast("无法加载积分记录，容器不存在");
                                    return;
                                }

                                // 清空容器
                                recordContainer.removeAllViews();
                                console.log("已清空积分记录容器");

                                // 创建并添加记录项
                                if (pointsRecords && pointsRecords.length > 0) {
                                    console.log("开始添加" + pointsRecords.length + "条积分记录");

                                    // 创建简单的字符串模板
                                    var recordTemplate =
                                        '<horizontal padding="10 15" bg="#ffffff" margin="0 5">' +
                                        '<vertical layout_weight="1">' +
                                        '<text text="%description%" textSize="14sp" textColor="#333333"/>' +
                                        '<text text="%time%" textSize="12sp" textColor="#999999" marginTop="3"/>' +
                                        '</vertical>' +
                                        '<text text="%amount%" textSize="16sp" textColor="%color%" textStyle="bold"/>' +
                                        '</horizontal>';

                                    for (var i = 0; i < pointsRecords.length; i++) {
                                        try {
                                            var record = pointsRecords[i];
                                            var isPositive = record.pointsChange > 0;
                                            var amountText = (isPositive ? "+" : "") + record.pointsChange;
                                            var color = isPositive ? "#4CAF50" : "#F44336";

                                            // 替换模板中的占位符
                                            var itemXml = recordTemplate
                                                .replace("%description%", record.description || record.operationTypeName || "积分变动")
                                                .replace("%time%", record.createTime || "")
                                                .replace("%amount%", amountText)
                                                .replace("%color%", color);

                                            // 创建记录项视图
                                            var recordView = ui.inflate(itemXml, recordContainer);
                                            recordContainer.addView(recordView);

                                            console.log("已添加第" + (i + 1) + "条积分记录");
                                        } catch (itemError) {
                                            console.error("添加积分记录项失败: " + itemError.message);
                                        }
                                    }
                                    console.log("积分记录添加完成");
                                } else {
                                    // 没有记录时显示提示
                                    var emptyView = ui.inflate(
                                        '<text text="暂无积分记录" textSize="14sp" textColor="#999999" gravity="center" margin="0 20"/>',
                                        recordContainer
                                    );
                                    recordContainer.addView(emptyView);
                                    console.log("已显示暂无积分记录提示");
                                }

                                LogModule.log("积分数据加载完成");
                                console.log("积分数据UI更新完成");
                            } catch (uiError) {
                                console.error("更新积分UI失败: " + uiError.message);
                                console.error(uiError.stack);
                                LogModule.log("更新积分UI失败: " + uiError.message, "ERROR");
                                toast("更新积分UI失败，请重试");
                            }
                        });
                    } else {
                        console.error("获取积分数据失败: " + (result ? result.message : "未知错误"));
                        toast("获取积分数据失败: " + (result ? result.message : "未知错误"));
                        LogModule.log("获取积分数据失败: " + (result ? result.message : "未知错误"), "ERROR");
                    }
                }, token);

            } catch (e) {
                console.error("加载积分数据失败: " + e.message);
                console.error(e.stack);
                LogModule.log("加载积分数据失败: " + e.message, "ERROR");
                toast("加载积分数据失败: " + e.message);
            }
        }
    };
})();

// 添加积分兑换模块
const pointsExchangeModule = (function () {
    return {
        /**
         * 显示积分兑换页面
         */
        showPointsExchangePage: function () {
            try {
                console.log("开始显示积分兑换页面");

                // 创建积分兑换页面UI
                var pointsExchangeLayoutXml =
                    '<frame bg="#f5f5f5">' +
                    '<vertical padding="0" h="*">' +
                    '<horizontal gravity="center_vertical" h="50" bg="#ffffff">' +
                    '<button id="backFromExchangeBtn" text="返回" textSize="14sp" style="Widget.AppCompat.Button.Borderless" w="60dp" h="40dp"/>' +
                    '<text text="积分兑换" textSize="18sp" textColor="#333333" gravity="center" layout_weight="1" textStyle="bold"/>' +
                    '<frame w="60dp" visibility="invisible"/>' +
                    '</horizontal>' +

                    '<card cardCornerRadius="0dp" cardElevation="2dp" margin="0 10 0 0">' +
                    '<vertical padding="15 10">' +
                    '<horizontal>' +
                    '<text text="当前积分：" textSize="16sp" textColor="#333333"/>' +
                    '<text id="exchangeCurrentPointsText" text="0" textSize="16sp" textColor="#FF5722" textStyle="bold"/>' +
                    '</horizontal>' +
                    '</vertical>' +
                    '</card>' +

                    '<scroll layout_weight="1">' +
                    '<vertical id="cardListContainer" padding="0">' +
                    '<text text="加载中..." textSize="14sp" textColor="#999999" gravity="center" margin="0 50"/>' +
                    '</vertical>' +
                    '</scroll>' +
                    '</vertical>' +
                    '</frame>';

                // 设置UI布局
                ui.layout(pointsExchangeLayoutXml);

                // 设置当前页面状态
                UIModule.setCurrentPage("pointsExchange");

                // 绑定返回按钮事件
                ui.backFromExchangeBtn.on("click", function () {
                    console.log("积分兑换页面返回按钮被点击");
                    // 直接返回脚本中心并切换到"我的"标签
                    UIModule.returnToScriptCenterMyTab();
                });

                // 加载用户积分数据
                this.loadUserPointsData();

                // 加载积分兑换配置
                this.loadExchangeConfigs();

                LogModule.log("积分兑换页面已显示");
            } catch (e) {
                console.error("显示积分兑换页面失败: " + e.message);
                console.error(e.stack);
                toast("显示积分兑换页面失败: " + e.message);
            }
        },

        /**
         * 加载用户积分数据
         */
        loadUserPointsData: function () {
            try {
                console.log("开始加载用户积分数据...");
                var userPhone = ConfigModule.get("userPhone");
                var token = ConfigModule.get("userToken");

                if (!userPhone || !token) {
                    console.log("用户信息不完整，无法加载积分数据");
                    toast("用户信息不完整，无法加载积分数据");
                    LogModule.log("加载积分数据失败：用户信息不完整", "ERROR");
                    return;
                }

                // 调用API获取积分数据
                NetworkModule.post("/points/external/user", { phone: userPhone }, function (error, result) {
                    if (error) {
                        console.error("获取积分数据失败: " + error.message);
                        toast("获取积分数据失败，请检查网络连接");
                        LogModule.log("获取积分数据失败: " + error.message, "ERROR");
                        return;
                    }

                    if (result && result.code === 200 && result.data) {
                        console.log("积分数据获取成功，开始更新UI");

                        // 更新UI - 使用ui.run确保在UI线程中执行
                        ui.run(function () {
                            try {
                                var pointsData = result.data;
                                var userPoints = pointsData.userPoints || {};

                                // 更新当前积分
                                var currentPointsText = ui.findById("exchangeCurrentPointsText");
                                if (currentPointsText) {
                                    currentPointsText.setText(userPoints.points ? userPoints.points.toString() : "0");
                                }

                                console.log("用户积分数据UI更新完成");
                            } catch (uiError) {
                                console.error("更新积分UI失败: " + uiError.message);
                                console.error(uiError.stack);
                                LogModule.log("更新积分UI失败: " + uiError.message, "ERROR");
                            }
                        });
                    } else {
                        console.error("获取积分数据失败: " + (result ? result.message : "未知错误"));
                        LogModule.log("获取积分数据失败: " + (result ? result.message : "未知错误"), "ERROR");
                    }
                }, token);

            } catch (e) {
                console.error("加载用户积分数据失败: " + e.message);
                console.error(e.stack);
                LogModule.log("加载用户积分数据失败: " + e.message, "ERROR");
            }
        },

        /**
         * 加载积分兑换配置
         */
        loadExchangeConfigs: function () {
            try {
                console.log("开始加载积分兑换配置...");
                var token = ConfigModule.get("userToken");

                if (!token) {
                    console.log("用户未登录，无法加载兑换配置");
                    toast("用户未登录，无法加载兑换配置");
                    LogModule.log("加载兑换配置失败：用户未登录", "ERROR");
                    return;
                }

                // 调用API获取积分配置
                NetworkModule.get("/points/config/list", null, function (error, result) {
                    if (error) {
                        console.error("获取积分配置失败: " + error.message);
                        toast("获取积分配置失败，请检查网络连接");
                        LogModule.log("获取积分配置失败: " + error.message, "ERROR");
                        return;
                    }

                    if (result && result.code === 200 && result.data) {
                        console.log("积分配置获取成功，开始更新UI");

                        // 过滤出兑换卡密相关的配置
                        var exchangeConfigs = result.data.filter(function (config) {
                            return config.configKey && config.configKey.startsWith("EXCHANGE_POINTS_TYPE_");
                        });

                        console.log("找到" + exchangeConfigs.length + "个兑换配置项");

                        // 更新UI - 使用ui.run确保在UI线程中执行
                        ui.run(function () {
                            try {
                                var cardListContainer = ui.findById("cardListContainer");
                                if (!cardListContainer) {
                                    console.error("找不到卡片列表容器");
                                    return;
                                }

                                // 清空容器
                                cardListContainer.removeAllViews();

                                if (exchangeConfigs.length === 0) {
                                    // 没有配置时显示提示
                                    var emptyView = ui.inflate(
                                        '<text text="暂无可兑换的卡密" textSize="14sp" textColor="#999999" gravity="center" margin="0 50"/>',
                                        cardListContainer
                                    );
                                    cardListContainer.addView(emptyView);
                                    return;
                                }

                                // 设置容器内边距
                                cardListContainer.setPadding(0, 0, 0, 0);

                                // 创建卡片列表
                                for (var i = 0; i < exchangeConfigs.length; i++) {
                                    try {
                                        var config = exchangeConfigs[i];
                                        var keyType = config.configKey.replace("EXCHANGE_POINTS_TYPE_", "");
                                        var pointsRequired = config.configValue;
                                        var cardName = config.configName;
                                        var cardRemark = config.remark || "积分兑换卡密";

                                        // 尝试从remark字段中提取图片URL
                                        var imageUrl = null;
                                        try {
                                            // 检查remark是否包含http或https链接
                                            if (cardRemark && (cardRemark.includes("http://") || cardRemark.includes("https://"))) {
                                                // 简单提取URL，假设remark中包含完整URL
                                                var urlMatch = cardRemark.match(/(https?:\/\/[^\s]+)/g);
                                                if (urlMatch && urlMatch.length > 0) {
                                                    imageUrl = urlMatch[0];
                                                    console.log("从remark中提取到图片URL: " + imageUrl);
                                                } else {
                                                    // 如果没有匹配到URL但包含http或https，尝试将整个remark作为URL
                                                    imageUrl = cardRemark.trim();
                                                    console.log("使用整个remark作为图片URL: " + imageUrl);
                                                }
                                            }
                                        } catch (e) {
                                            console.error("提取图片URL失败: " + e.message);
                                        }

                                        // 创建卡片视图
                                        var cardXml =
                                            '<card cardCornerRadius="8dp" cardElevation="2dp" margin="8 5" foreground="?selectableItemBackground">' +
                                            '<vertical padding="0">' +
                                            '<frame layout_gravity="center" margin="0" padding="0">' +
                                            '<img id="cardImg_' + keyType + '" src="@android:drawable/ic_menu_report_image" w="*" h="180dp" scaleType="centerCrop"/>' +
                                            '</frame>' +
                                            '<vertical padding="12 8">' +
                                            '<text text="' + cardName + '" textSize="16sp" textColor="#333333" textStyle="bold" gravity="center" margin="0 2"/>' +
                                            '<text text="所需积分: ' + pointsRequired + '" textSize="14sp" textColor="#FF5722" gravity="center" margin="0 5"/>' +
                                            '<button id="exchangeBtn_' + keyType + '" text="立即兑换" textSize="14sp" style="Widget.AppCompat.Button.Colored" margin="0 5 0 0"/>' +
                                            '</vertical>' +
                                            '</vertical>' +
                                            '</card>';

                                        var cardView = ui.inflate(cardXml, cardListContainer);
                                        cardListContainer.addView(cardView);

                                        // 存储卡片数据，用于后续兑换操作
                                        cardView.tag = {
                                            keyType: parseInt(keyType),
                                            pointsRequired: parseInt(pointsRequired),
                                            cardName: cardName,
                                            remark: cardRemark,
                                            imageUrl: imageUrl
                                        };

                                        // 如果有图片URL，加载图片
                                        if (imageUrl) {
                                            var imgId = "cardImg_" + keyType;
                                            var imgView = ui.findById(imgId);
                                            if (imgView) {
                                                // 设置加载中的占位图
                                                try {
                                                    // 设置加载中的占位图标
                                                    imgView.setImageResource(android.R.drawable.ic_menu_gallery);
                                                } catch (e) {
                                                    console.error("设置占位图失败: " + e.message);
                                                }

                                                // 使用IIFE解决闭包问题，确保每个线程使用正确的URL和控件
                                                (function (currentImgView, currentImgUrl) {
                                                    console.log("准备加载图片: " + currentImgUrl + " 到控件: " + imgId);

                                                    // 在子线程中加载图片，避免NetworkOnMainThreadException
                                                    threads.start(function () {
                                                        try {
                                                            console.log("开始加载图片: " + currentImgUrl);

                                                            // 设置加载超时
                                                            let loadingTimeout = setTimeout(function () {
                                                                console.log("图片加载超时: " + currentImgUrl);
                                                                ui.run(function () {
                                                                    try {
                                                                        currentImgView.setImageResource(android.R.drawable.ic_menu_gallery);
                                                                    } catch (e) {
                                                                        console.error("设置默认图片失败: " + e.message);
                                                                    }
                                                                });
                                                            }, 10000); // 10秒超时

                                                            // 使用原生方法加载图片
                                                            let imgWrapper = images.load(currentImgUrl);

                                                            // 清除超时
                                                            clearTimeout(loadingTimeout);

                                                            if (imgWrapper) {
                                                                // 将ImageWrapper转换为Bitmap
                                                                let bitmap = imgWrapper.getBitmap();

                                                                // 在UI线程中设置图片
                                                                ui.run(function () {
                                                                    try {
                                                                        currentImgView.setImageBitmap(bitmap);
                                                                        console.log("图片加载成功: " + currentImgUrl);

                                                                        // 不再需要圆角处理，直接使用原始图片
                                                                    } catch (e) {
                                                                        console.error("设置图片到UI失败: " + e.message);

                                                                        // 加载失败时设置默认图标
                                                                        try {
                                                                            currentImgView.setImageResource(android.R.drawable.ic_menu_gallery);
                                                                        } catch (defaultError) {
                                                                            console.error("设置默认图标也失败: " + defaultError.message);
                                                                        }
                                                                    }
                                                                });
                                                            } else {
                                                                console.error("加载图片失败: 无法获取位图");

                                                                // 在UI线程中设置默认图片
                                                                ui.run(function () {
                                                                    try {
                                                                        currentImgView.setImageResource(android.R.drawable.ic_menu_gallery);
                                                                    } catch (e) {
                                                                        console.error("设置默认图片失败: " + e.message);
                                                                    }
                                                                });
                                                            }
                                                        } catch (threadError) {
                                                            console.error("子线程加载图片失败: " + threadError.message);
                                                        }
                                                    });
                                                })(imgView, imageUrl);
                                            }
                                        }

                                        // 绑定兑换按钮点击事件
                                        var btnId = "exchangeBtn_" + keyType;
                                        var exchangeBtn = ui.findById(btnId);
                                        if (exchangeBtn) {
                                            console.log("找到兑换按钮: " + btnId);

                                            // 使用直接绑定方式，避免闭包问题
                                            (function (currentCardData) {
                                                exchangeBtn.on("click", function () {
                                                    console.log("兑换按钮被点击: " + currentCardData.cardName);
                                                    console.log("兑换数据: " + JSON.stringify(currentCardData));
                                                    pointsExchangeModule.showExchangeConfirmDialog(currentCardData);
                                                });
                                            })(cardView.tag);
                                        } else {
                                            console.error("未找到兑换按钮: " + btnId);
                                        }

                                        console.log("已添加卡片: " + cardName);
                                    } catch (cardError) {
                                        console.error("添加卡片失败: " + cardError.message);
                                    }
                                }

                                console.log("积分兑换配置UI更新完成");
                            } catch (uiError) {
                                console.error("更新积分兑换UI失败: " + uiError.message);
                                console.error(uiError.stack);
                                LogModule.log("更新积分兑换UI失败: " + uiError.message, "ERROR");

                                // 显示错误提示
                                var errorView = ui.inflate(
                                    '<text text="加载兑换配置失败，请重试" textSize="14sp" textColor="#F44336" gravity="center" margin="0 50"/>',
                                    cardListContainer
                                );
                                cardListContainer.removeAllViews();
                                cardListContainer.addView(errorView);
                            }
                        });
                    } else {
                        console.error("获取积分配置失败: " + (result ? result.message : "未知错误"));
                        LogModule.log("获取积分配置失败: " + (result ? result.message : "未知错误"), "ERROR");

                        // 显示错误提示
                        ui.run(function () {
                            var cardListContainer = ui.findById("cardListContainer");
                            if (cardListContainer) {
                                var errorView = ui.inflate(
                                    '<text text="加载兑换配置失败，请重试" textSize="14sp" textColor="#F44336" gravity="center" margin="0 50"/>',
                                    cardListContainer
                                );
                                cardListContainer.removeAllViews();
                                cardListContainer.addView(errorView);
                            }
                        });
                    }
                }, token);

            } catch (e) {
                console.error("加载积分兑换配置失败: " + e.message);
                console.error(e.stack);
                LogModule.log("加载积分兑换配置失败: " + e.message, "ERROR");

                // 显示错误提示
                ui.run(function () {
                    var cardListContainer = ui.findById("cardListContainer");
                    if (cardListContainer) {
                        var errorView = ui.inflate(
                            '<text text="加载兑换配置失败，请重试" textSize="14sp" textColor="#F44336" gravity="center" margin="0 50"/>',
                            cardListContainer
                        );
                        cardListContainer.removeAllViews();
                        cardListContainer.addView(errorView);
                    }
                });
            }
        },

        /**
         * 显示兑换确认对话框
         * @param {Object} cardData - 卡片数据
         */
        showExchangeConfirmDialog: function (cardData) {
            try {
                console.log("显示兑换确认对话框开始执行");
                console.log("卡片数据: " + JSON.stringify(cardData));

                // 获取当前积分
                var currentPointsText = ui.findById("exchangeCurrentPointsText");
                var currentPoints = 0;
                if (currentPointsText) {
                    try {
                        currentPoints = parseInt(currentPointsText.getText().toString());
                        console.log("当前积分: " + currentPoints);
                    } catch (parseError) {
                        console.error("解析积分失败: " + parseError.message);
                        currentPoints = 0;
                    }
                } else {
                    console.error("未找到积分显示控件");
                }

                // 检查积分是否足够
                if (currentPoints < cardData.pointsRequired) {
                    console.log("积分不足: 当前" + currentPoints + ", 需要" + cardData.pointsRequired);
                    toast("积分不足，无法兑换");
                    return;
                }

                console.log("积分充足，显示确认对话框");

                // 显示确认对话框
                ui.run(function () {
                    try {
                        dialogs.build({
                            title: "兑换确认",
                            content: "确定使用 " + cardData.pointsRequired + " 积分兑换「" + cardData.cardName + "」吗？",
                            positive: "确定兑换",
                            negative: "取消",
                            cancelable: false
                        })
                            .on("positive", () => {
                                console.log("用户确认兑换");
                                // 执行兑换操作
                                pointsExchangeModule.exchangeCardKey(cardData);
                            })
                            .on("negative", () => {
                                console.log("用户取消兑换");
                            })
                            .show();
                        console.log("确认对话框已显示");
                    } catch (dialogError) {
                        console.error("显示确认对话框失败: " + dialogError.message);
                        toast("显示确认对话框失败");
                    }
                });
            } catch (e) {
                console.error("显示兑换确认对话框失败: " + e.message);
                console.error(e.stack);
                toast("显示兑换确认对话框失败");
            }
        },

        /**
         * 执行卡密兑换
         * @param {Object} cardData - 卡片数据
         */
        exchangeCardKey: function (cardData) {
            try {
                console.log("开始执行卡密兑换...");
                console.log("兑换数据: " + JSON.stringify(cardData));

                var token = ConfigModule.get("userToken");
                var userId = ConfigModule.get("userId");

                console.log("用户ID: " + userId + ", Token是否存在: " + (token ? "是" : "否"));

                if (!token || !userId) {
                    console.log("用户信息不完整，无法执行兑换");
                    toast("用户信息不完整，无法执行兑换");
                    return;
                }

                // 显示加载提示
                toast("正在兑换，请稍候...");

                // 构建请求数据
                var requestData = {
                    userId: parseInt(userId),
                    keyType: cardData.keyType,
                    experienceHours: null,
                    remark: cardData.remark || "积分兑换卡密"
                };

                console.log("请求数据: " + JSON.stringify(requestData));

                // 调用API执行兑换
                NetworkModule.post("/points/exchange-card-key", requestData, function (error, result) {
                    console.log("兑换API返回: " + (error ? "错误: " + error.message : "成功"));

                    if (error) {
                        console.error("兑换卡密失败: " + error.message);
                        toast("兑换卡密失败，请检查网络连接");
                        LogModule.log("兑换卡密失败: " + error.message, "ERROR");
                        return;
                    }

                    console.log("API响应: " + JSON.stringify(result));

                    if (result && result.code === 200 && result.data) {
                        console.log("卡密兑换成功: " + JSON.stringify(result.data));

                        // 显示兑换成功对话框
                        var keyCode = result.data.keyCode;
                        ui.run(function () {
                            try {
                                dialogs.build({
                                    title: "兑换成功",
                                    content: "恭喜您成功兑换卡密！\n\n卡密：" + keyCode + "\n\n请妥善保管您的卡密。",
                                    positive: "复制卡密",
                                    negative: "关闭",
                                    cancelable: false
                                })
                                    .on("positive", () => {
                                        // 复制卡密到剪贴板
                                        setClip(keyCode);
                                        toast("卡密已复制到剪贴板");
                                    })
                                    .on("dismiss", () => {
                                        // 刷新积分数据
                                        pointsExchangeModule.loadUserPointsData();
                                    })
                                    .show();
                            } catch (dialogError) {
                                console.error("显示兑换成功对话框失败: " + dialogError.message);
                                toast("兑换成功，但显示结果失败");
                            }
                        });

                        LogModule.log("卡密兑换成功: " + keyCode);
                    } else {
                        console.error("兑换卡密失败: " + (result ? result.message : "未知错误"));
                        toast("兑换卡密失败: " + (result ? result.message : "未知错误"));
                        LogModule.log("兑换卡密失败: " + (result ? result.message : "未知错误"), "ERROR");
                    }
                }, token);

                console.log("兑换请求已发送，等待响应...");

            } catch (e) {
                console.error("执行卡密兑换失败: " + e.message);
                console.error(e.stack);
                toast("执行卡密兑换失败: " + e.message);
                LogModule.log("执行卡密兑换失败: " + e.message, "ERROR");
            }
        }
    };
})();
// 全局函数 - 显示提现页面
function showWithdrawalPage() {
    try {
        console.log("全局函数showWithdrawalPage被调用");
        LogModule.log("全局函数showWithdrawalPage被调用", "INFO");

        // 确保UIModule已定义
        if (typeof UIModule === 'undefined') {
            console.error("UIModule未定义");
            LogModule.log("UIModule未定义", "ERROR");
            toast("加载提现页面失败: UIModule未定义");
            return;
        }

        // 确保在UI线程中执行
        ui.run(function () {
            try {
                console.log("准备在UI线程中创建提现页面");
                LogModule.log("准备在UI线程中创建提现页面", "INFO");

                // 尝试创建提现页面
                UIModule.createWithdrawalPage();
                console.log("UIModule.createWithdrawalPage()调用完成 - 全局函数");
                LogModule.log("提现页面创建完成", "INFO");
            } catch (e) {
                console.error("UI线程中执行createWithdrawalPage失败: " + e.message);
                console.error(e.stack);
                LogModule.log("UI线程中执行createWithdrawalPage失败: " + e.message, "ERROR");
                LogModule.log(e.stack, "ERROR");
                toast("加载提现页面失败: " + e.message);
            }
        });
    } catch (e) {
        console.error("全局函数showWithdrawalPage执行失败: " + e.message);
        console.error(e.stack);
        LogModule.log("全局函数showWithdrawalPage执行失败: " + e.message, "ERROR");
        LogModule.log(e.stack, "ERROR");
        toast("加载提现页面失败: " + e.message);
    }
}

// 文件末尾的初始化代码
// 确保所有模块已定义后再执行初始化
try {
    console.log("开始初始化应用...");

    // 检查必需模块是否已定义
    if (typeof LogModule === 'undefined') {
        throw new Error("LogModule未定义");
    }

    if (typeof StorageModule === 'undefined') {
        throw new Error("StorageModule未定义");
    }

    if (typeof ConfigModule === 'undefined') {
        throw new Error("ConfigModule未定义");
    }

    if (typeof PermissionModule === 'undefined') {
        throw new Error("PermissionModule未定义");
    }

    if (typeof NetworkModule === 'undefined') {
        throw new Error("NetworkModule未定义");
    }

    if (typeof UIModule === 'undefined') {
        throw new Error("UIModule未定义");
    }

    if (typeof PacketCaptureDetector === 'undefined') {
        throw new Error("PacketCaptureDetector未定义");
    }

    if (typeof MainModule === 'undefined') {
        throw new Error("MainModule未定义");
    }

    // 按顺序初始化应用
    console.log("所有模块已定义，开始初始化应用");
    // 初始化应用
    MainModule.init();
    console.log("应用初始化完成，开始启动应用");
    // 启动应用
    MainModule.start();
    console.log("应用启动完成");


} catch (e) {
    console.error("应用初始化失败: " + e.message);
    console.error(e.stack);
    toast("应用初始化失败: " + e.message);
}




