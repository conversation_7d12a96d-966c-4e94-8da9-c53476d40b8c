"ui";
//点赞按钮
var likeButton;
//喜欢按钮
var favoriteButton;
try {
    //寻找点赞按钮
    likeButton = className("android.widget.Button").textContains("赞").findOne(6000);
    //喜欢按钮
    favoriteButton = className("android.widget.Button").textContains("在看").findOne(6000);
} catch (e) {
    //如果没找到说明不在文章页面
    that.toast("当前不在文章页面");
}
setTimeout(() => {
    likeButton.click();
    console.log("点赞按钮被按下",likeButton.text());
    console.log("点赞按钮被按下",likeButton.clickable());
}, 3000);
